# Django Framework
Django==5.1.0

# Database
psycopg2-binary==2.9.7  # PostgreSQL adapter

# Authentication
django-allauth==0.57.0

# Rich Text Editor
django-ckeditor==6.7.0

# Forms
django-crispy-forms==2.0
crispy-bootstrap5==0.7

# Image Processing
Pillow==10.0.0

# Utilities
python-decouple==3.8  # Environment variables
django-extensions==3.2.3  # Useful extensions

# Development Tools (optional)
django-debug-toolbar==4.2.0  # Debug toolbar for development

# Production (optional)
gunicorn==21.2.0  # WSGI server for production
whitenoise==6.5.0  # Static files serving

# Email (optional)
django-ses==3.5.0  # Amazon SES integration

# Caching (optional)
redis==4.6.0  # Redis for caching
django-redis==5.3.0  # Django Redis integration

# API (optional for future)
djangorestframework==3.14.0  # REST API framework
django-cors-headers==4.2.0  # CORS handling

# Security
django-ratelimit==4.1.0  # Rate limiting
django-axes==6.1.1  # Brute force protection

# Monitoring (optional)
sentry-sdk==1.32.0  # Error tracking

# Testing
coverage==7.3.0  # Code coverage
factory-boy==3.3.0  # Test data generation

# Documentation
Sphinx==7.1.2  # Documentation generator