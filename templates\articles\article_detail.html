{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ article.title }} - تاتا نيوز{% endblock %}

{% block meta_description %}{{ article.excerpt|truncatewords:30 }}{% endblock %}
{% block meta_keywords %}{{ article.tags.all|join:", " }}{% endblock %}

{% block og_title %}{{ article.title }}{% endblock %}
{% block og_description %}{{ article.excerpt|truncatewords:30 }}{% endblock %}
{% block og_image %}{% if article.featured_image %}{{ request.build_absolute_uri }}{{ article.featured_image.url }}{% endif %}{% endblock %}
{% block og_type %}article{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <!-- Article Content -->
        <div class="col-lg-8">
            <article class="article-detail">
                <!-- Article Header -->
                <header class="mb-4">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{% url 'articles:home' %}">الرئيسية</a></li>
                            <li class="breadcrumb-item"><a href="{{ article.category.get_absolute_url }}">{{ article.category.name }}</a></li>
                            <li class="breadcrumb-item active">{{ article.title|truncatewords:5 }}</li>
                        </ol>
                    </nav>
                    
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge me-2" style="background-color: {{ article.category.color }};">
                            {{ article.category.name }}
                        </span>
                        {% if article.is_breaking_news %}
                            <span class="badge bg-danger me-2">عاجل</span>
                        {% endif %}
                        {% if article.is_featured %}
                            <span class="badge bg-warning text-dark">مميز</span>
                        {% endif %}
                    </div>
                    
                    <h1 class="article-title mb-3">{{ article.title }}</h1>
                    
                    {% if article.subtitle %}
                        <h2 class="article-subtitle text-muted mb-4">{{ article.subtitle }}</h2>
                    {% endif %}
                    
                    <div class="article-meta d-flex justify-content-between align-items-center mb-4 p-3 bg-light rounded">
                        <div class="author-info d-flex align-items-center">
                            {% if article.author.avatar %}
                                <img src="{{ article.author.avatar.url }}" alt="{{ article.author.get_full_name }}" class="rounded-circle me-3" width="50" height="50">
                            {% else %}
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                    {{ article.author.first_name.0 }}{{ article.author.last_name.0 }}
                                </div>
                            {% endif %}
                            <div>
                                <strong>
                                    <a href="{% url 'articles:author_articles' article.author.id %}" class="text-decoration-none">
                                        {{ article.author.get_full_name }}
                                    </a>
                                </strong>
                                <br>
                                <small class="text-muted">{{ article.author.role|capfirst }}</small>
                            </div>
                        </div>
                        <div class="article-stats text-end">
                            <div><i class="fas fa-calendar"></i> {{ article.published_at|date:"d M Y - H:i" }}</div>
                            <div><i class="fas fa-eye"></i> {{ article.views_count }} مشاهدة</div>
                            <div><i class="fas fa-heart"></i> {{ article.likes_count }} إعجاب</div>
                        </div>
                    </div>
                </header>
                
                <!-- Featured Image -->
                {% if article.featured_image %}
                <div class="featured-image mb-4">
                    <img src="{{ article.featured_image.url }}" alt="{{ article.image_alt|default:article.title }}" class="img-fluid rounded">
                    {% if article.image_alt %}
                        <small class="text-muted d-block mt-2">{{ article.image_alt }}</small>
                    {% endif %}
                </div>
                {% endif %}
                
                <!-- Article Content -->
                <div class="article-content">
                    {{ article.content|safe }}
                </div>
                
                <!-- Tags -->
                {% if article.tags.exists %}
                <div class="article-tags mt-4 pt-4 border-top">
                    <h6>العلامات:</h6>
                    {% for tag in article.tags.all %}
                        <a href="{% url 'articles:tag_detail' tag.slug %}" class="tag">#{{ tag.name }}</a>
                    {% endfor %}
                </div>
                {% endif %}
                
                <!-- Article Actions -->
                <div class="article-actions d-flex justify-content-between align-items-center mt-4 pt-4 border-top">
                    <div class="social-share">
                        <h6>شارك المقال:</h6>
                        <button class="btn btn-outline-primary btn-sm share-btn" data-platform="facebook">
                            <i class="fab fa-facebook-f"></i> فيسبوك
                        </button>
                        <button class="btn btn-outline-info btn-sm share-btn" data-platform="twitter">
                            <i class="fab fa-twitter"></i> تويتر
                        </button>
                        <button class="btn btn-outline-success btn-sm share-btn" data-platform="whatsapp">
                            <i class="fab fa-whatsapp"></i> واتساب
                        </button>
                        <button class="btn btn-outline-secondary btn-sm share-btn" data-platform="copy">
                            <i class="fas fa-copy"></i> نسخ الرابط
                        </button>
                    </div>
                    
                    <div class="article-like">
                        {% if user.is_authenticated %}
                            <button class="btn btn-outline-danger like-btn {% if user_liked %}liked{% endif %}" data-article-id="{{ article.id }}">
                                <i class="{% if user_liked %}fas{% else %}far{% endif %} fa-heart"></i>
                                <span class="like-count">{{ article.likes_count }}</span>
                            </button>
                        {% else %}
                            <a href="{% url 'account_login' %}" class="btn btn-outline-danger">
                                <i class="far fa-heart"></i> {{ article.likes_count }}
                            </a>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Author Bio -->
                {% if article.author.bio %}
                <div class="author-bio mt-5 p-4 bg-light rounded">
                    <div class="d-flex">
                        {% if article.author.avatar %}
                            <img src="{{ article.author.avatar.url }}" alt="{{ article.author.get_full_name }}" class="rounded-circle me-3" width="80" height="80">
                        {% endif %}
                        <div>
                            <h5>{{ article.author.get_full_name }}</h5>
                            <p class="text-muted">{{ article.author.bio }}</p>
                            <a href="{% url 'articles:author_articles' article.author.id %}" class="btn btn-outline-primary btn-sm">
                                عرض جميع مقالات الكاتب
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Comments Section -->
                {% if article.allow_comments %}
                <div class="comments-section mt-5">
                    <h4>التعليقات ({{ comments.count }})</h4>
                    
                    <!-- Add Comment Form -->
                    {% if user.is_authenticated %}
                    <div class="add-comment mb-4 p-4 bg-light rounded">
                        <h5>إضافة تعليق</h5>
                        <form method="POST" action="{% url 'articles:add_comment' article.id %}" class="comment-form">
                            {% csrf_token %}
                            <div class="mb-3">
                                <textarea name="content" class="form-control" rows="4" placeholder="اكتب تعليقك هنا..." required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">إرسال التعليق</button>
                        </form>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <a href="{% url 'account_login' %}">سجل الدخول</a> لإضافة تعليق
                    </div>
                    {% endif %}
                    
                    <!-- Comments List -->
                    <div class="comments-list">
                        {% for comment in comments %}
                        <div class="comment mb-4" id="comment-{{ comment.id }}">
                            <div class="d-flex">
                                {% if comment.author.avatar %}
                                    <img src="{{ comment.author.avatar.url }}" alt="{{ comment.author.get_full_name }}" class="rounded-circle me-3" width="50" height="50">
                                {% else %}
                                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                        {{ comment.author.first_name.0 }}{{ comment.author.last_name.0 }}
                                    </div>
                                {% endif %}
                                <div class="flex-grow-1">
                                    <div class="comment-header d-flex justify-content-between align-items-center mb-2">
                                        <div>
                                            <strong class="comment-author">{{ comment.author.get_full_name }}</strong>
                                            <small class="comment-date text-muted">{{ comment.created_at|date:"d M Y - H:i" }}</small>
                                        </div>
                                        <div class="comment-actions">
                                            {% if user.is_authenticated %}
                                                <button class="btn btn-sm btn-outline-primary comment-like-btn" data-comment-id="{{ comment.id }}">
                                                    <i class="far fa-thumbs-up"></i>
                                                    <span class="like-count">{{ comment.likes_count }}</span>
                                                </button>
                                                <button class="btn btn-sm btn-outline-secondary reply-btn" data-comment-id="{{ comment.id }}">
                                                    <i class="fas fa-reply"></i> رد
                                                </button>
                                            {% endif %}
                                            {% if user == comment.author %}
                                                <a href="{% url 'comments:edit' comment.id %}" class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'comments:delete' comment.id %}" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="comment-content">
                                        {{ comment.content|linebreaks }}
                                    </div>
                                    
                                    <!-- Reply Form -->
                                    {% if user.is_authenticated %}
                                    <div class="reply-form mt-3" id="reply-form-{{ comment.id }}" style="display: none;">
                                        <form method="POST" action="{% url 'comments:reply' comment.id %}">
                                            {% csrf_token %}
                                            <div class="mb-2">
                                                <textarea name="content" class="form-control" rows="3" placeholder="اكتب ردك هنا..." required></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-sm">إرسال الرد</button>
                                            <button type="button" class="btn btn-secondary btn-sm" onclick="this.closest('.reply-form').style.display='none'">إلغاء</button>
                                        </form>
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Replies -->
                                    {% for reply in comment.get_replies %}
                                    <div class="comment-reply mt-3 ps-4 border-start border-primary">
                                        <div class="d-flex">
                                            {% if reply.author.avatar %}
                                                <img src="{{ reply.author.avatar.url }}" alt="{{ reply.author.get_full_name }}" class="rounded-circle me-2" width="40" height="40">
                                            {% else %}
                                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                                    {{ reply.author.first_name.0 }}{{ reply.author.last_name.0 }}
                                                </div>
                                            {% endif %}
                                            <div class="flex-grow-1">
                                                <div class="comment-header mb-1">
                                                    <strong class="comment-author">{{ reply.author.get_full_name }}</strong>
                                                    <small class="comment-date text-muted">{{ reply.created_at|date:"d M Y - H:i" }}</small>
                                                </div>
                                                <div class="comment-content">
                                                    {{ reply.content|linebreaks }}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </article>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Related Articles -->
            {% if related_articles %}
            <div class="sidebar">
                <h5>مقالات ذات صلة</h5>
                {% for related in related_articles %}
                <div class="popular-article">
                    {% if related.featured_image %}
                        <img src="{{ related.featured_image.url }}" alt="{{ related.title }}">
                    {% endif %}
                    <div>
                        <h6>
                            <a href="{{ related.get_absolute_url }}" class="text-decoration-none">
                                {{ related.title|truncatewords:8 }}
                            </a>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> {{ related.published_at|date:"d M Y" }}
                        </small>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            <!-- Table of Contents (if article is long) -->
            <div class="sidebar">
                <h5>محتويات المقال</h5>
                <div id="table-of-contents">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reading Progress Bar -->
<div class="reading-progress position-fixed top-0 start-0 bg-primary" style="height: 3px; z-index: 1001; width: 0%;"></div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Generate table of contents
    generateTableOfContents();
    
    // Initialize reading progress
    updateReadingProgress();
    window.addEventListener('scroll', updateReadingProgress);
});

function generateTableOfContents() {
    const content = document.querySelector('.article-content');
    const toc = document.getElementById('table-of-contents');
    const headings = content.querySelectorAll('h2, h3, h4');
    
    if (headings.length === 0) {
        toc.innerHTML = '<p class="text-muted">لا توجد عناوين فرعية</p>';
        return;
    }
    
    let tocHTML = '<ul class="list-unstyled">';
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        const level = parseInt(heading.tagName.charAt(1));
        const indent = level > 2 ? 'ms-3' : '';
        
        tocHTML += `<li class="${indent}"><a href="#${id}" class="text-decoration-none small">${heading.textContent}</a></li>`;
    });
    tocHTML += '</ul>';
    
    toc.innerHTML = tocHTML;
}

function updateReadingProgress() {
    const article = document.querySelector('.article-content');
    if (!article) return;
    
    const articleTop = article.offsetTop;
    const articleHeight = article.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;
    
    const progress = Math.min(
        Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
        1
    );
    
    const progressBar = document.querySelector('.reading-progress');
    if (progressBar) {
        progressBar.style.width = `${progress * 100}%`;
    }
}
</script>
{% endblock %}