{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ article.title }} - تاتا نيوز{% endblock %}

{% block meta_description %}{{ article.excerpt|truncatewords:30 }}{% endblock %}
{% block meta_keywords %}{{ article.tags.all|join:", " }}, تاتا نيوز, أخبار{% endblock %}

{% block og_title %}{{ article.title }}{% endblock %}
{% block og_description %}{{ article.excerpt|truncatewords:30 }}{% endblock %}
{% block og_image %}{% if article.featured_image %}{{ request.build_absolute_uri }}{{ article.featured_image.url }}{% endif %}{% endblock %}
{% block og_type %}article{% endblock %}

{% block body_class %}article-page{% endblock %}

{% block content %}
<div class="modern-article-page">
    <!-- Article Hero Section -->
    <section class="article-hero position-relative overflow-hidden"
             style="background: linear-gradient(135deg, var(--primary-50) 0%, var(--neutral-50) 100%); padding: 2rem 0;">
        <div class="container">
            <!-- Modern Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb modern-breadcrumb bg-transparent p-0 m-0">
                    <li class="breadcrumb-item">
                        <a href="{% url 'articles:home' %}" class="text-decoration-none d-flex align-items-center">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{{ article.category.get_absolute_url }}" class="text-decoration-none">
                            {% if article.category.icon %}
                                <i class="{{ article.category.icon }} me-1"></i>
                            {% endif %}
                            {{ article.category.name }}
                        </a>
                    </li>
                    <li class="breadcrumb-item active text-muted">{{ article.title|truncatewords:5 }}</li>
                </ol>
            </nav>

            <!-- Article Header -->
            <div class="article-header">
                <!-- Article Badges -->
                <div class="article-badges mb-3">
                    <span class="badge badge-modern px-3 py-2 me-2"
                          style="background: var(--primary-600); color: white; border-radius: var(--radius-lg);">
                        {% if article.category.icon %}
                            <i class="{{ article.category.icon }} me-1"></i>
                        {% endif %}
                        {{ article.category.name }}
                    </span>
                    {% if article.is_breaking_news %}
                        <span class="badge badge-modern bg-danger px-3 py-2 me-2"
                              style="border-radius: var(--radius-lg);">
                            <i class="fas fa-bolt me-1"></i>عاجل
                        </span>
                    {% endif %}
                    {% if article.is_featured %}
                        <span class="badge badge-modern px-3 py-2 me-2"
                              style="background: var(--accent-yellow); color: var(--neutral-900); border-radius: var(--radius-lg);">
                            <i class="fas fa-star me-1"></i>مميز
                        </span>
                    {% endif %}
                    <span class="badge badge-modern px-3 py-2"
                          style="background: var(--neutral-200); color: var(--neutral-700); border-radius: var(--radius-lg);">
                        <i class="fas fa-clock me-1"></i>{{ article.content|wordcount|floatformat:0|add:"50"|div:200 }} دقائق قراءة
                    </span>
                </div>

                <!-- Article Title -->
                <h1 class="article-title mb-3"
                    style="font-size: 2.5rem; font-weight: 700; line-height: 1.2; color: var(--neutral-900);">
                    {{ article.title }}
                </h1>

                <!-- Article Subtitle -->
                {% if article.subtitle %}
                    <h2 class="article-subtitle mb-4 text-muted"
                        style="font-size: 1.25rem; font-weight: 400; line-height: 1.4;">
                        {{ article.subtitle }}
                    </h2>
                {% endif %}

                <!-- Article Meta -->
                <div class="article-meta modern-card p-4 mb-4"
                     style="border-radius: var(--radius-xl); background: white; border: 1px solid var(--neutral-200);">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="author-info d-flex align-items-center">
                                <!-- Author Avatar -->
                                <div class="author-avatar me-3"
                                     style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; font-weight: 600;">
                                    {{ article.author.get_full_name.0|default:article.author.username.0|upper }}
                                </div>

                                <div class="author-details">
                                    <div class="author-name" style="font-weight: 600; color: var(--neutral-900); margin-bottom: 0.25rem;">
                                        {{ article.author.get_full_name|default:article.author.username }}
                                    </div>
                                    <div class="article-date text-muted d-flex align-items-center" style="font-size: 0.875rem;">
                                        <i class="fas fa-calendar me-1"></i>
                                        <span>{{ article.published_at|date:"d M Y" }}</span>
                                        <span class="mx-2">•</span>
                                        <i class="fas fa-clock me-1"></i>
                                        <span>{{ article.published_at|date:"H:i" }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 text-md-end">
                            <div class="article-stats d-flex align-items-center justify-content-md-end gap-3">
                                <div class="stat-item d-flex align-items-center text-muted">
                                    <i class="fas fa-eye me-1"></i>
                                    <span>{{ article.views_count }}</span>
                                </div>
                                <div class="stat-item d-flex align-items-center text-muted">
                                    <i class="fas fa-heart me-1"></i>
                                    <span>{{ article.likes_count }}</span>
                                </div>
                                <div class="stat-item d-flex align-items-center text-muted">
                                    <i class="fas fa-share me-1"></i>
                                    <span>مشاركة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <div class="container py-5">
        <div class="row g-4">
            <!-- Article Content -->
            <div class="col-lg-8">
                <article class="modern-article-content">
                    <!-- Featured Image -->
                    {% if article.featured_image %}
                    <div class="article-image mb-5 position-relative overflow-hidden"
                         style="border-radius: var(--radius-2xl);">
                        <img src="{{ article.featured_image.url }}"
                             alt="{{ article.image_alt|default:article.title }}"
                             class="w-100 h-auto"
                             style="max-height: 500px; object-fit: cover;">

                        {% if article.image_alt %}
                        <div class="image-caption position-absolute bottom-0 start-0 end-0 p-3"
                             style="background: linear-gradient(transparent, rgba(0,0,0,0.7)); color: white;">
                            <small>{{ article.image_alt }}</small>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Article Content -->
                    <div class="article-content modern-card p-5 mb-5"
                         style="border-radius: var(--radius-2xl); background: white; border: 1px solid var(--neutral-200); line-height: 1.8; font-size: 1.125rem;">

                        <!-- Article Excerpt -->
                        {% if article.excerpt %}
                        <div class="article-excerpt p-4 mb-4"
                             style="background: var(--primary-50); border-left: 4px solid var(--primary-600); border-radius: var(--radius-lg); font-size: 1.25rem; font-style: italic; color: var(--neutral-700);">
                            {{ article.excerpt }}
                        </div>
                        {% endif %}

                        <!-- Main Content -->
                        <div class="content-body">
                            {{ article.content|safe }}
                        </div>
                    </div>

                    <!-- Article Actions -->
                    <div class="article-actions modern-card p-4 mb-5"
                         style="border-radius: var(--radius-xl); background: var(--neutral-50); border: 1px solid var(--neutral-200);">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <!-- Like and Share Buttons -->
                                <div class="d-flex align-items-center gap-3">
                                    <button class="btn btn-modern-secondary like-btn"
                                            data-article-id="{{ article.id }}"
                                            style="border-radius: var(--radius-lg);">
                                        <i class="fas fa-heart me-1"></i>
                                        <span class="like-count">{{ article.likes_count }}</span>
                                    </button>

                                    <div class="dropdown">
                                        <button class="btn btn-modern-secondary dropdown-toggle"
                                                type="button"
                                                data-bs-toggle="dropdown"
                                                style="border-radius: var(--radius-lg);">
                                            <i class="fas fa-share me-1"></i>مشاركة
                                        </button>
                                        <ul class="dropdown-menu modern-card border-0 mt-2">
                                            <li><a class="dropdown-item share-btn" href="#" data-platform="facebook">
                                                <i class="fab fa-facebook me-2 text-primary"></i>فيسبوك
                                            </a></li>
                                            <li><a class="dropdown-item share-btn" href="#" data-platform="twitter">
                                                <i class="fab fa-twitter me-2 text-info"></i>تويتر
                                            </a></li>
                                            <li><a class="dropdown-item share-btn" href="#" data-platform="whatsapp">
                                                <i class="fab fa-whatsapp me-2 text-success"></i>واتساب
                                            </a></li>
                                            <li><a class="dropdown-item share-btn" href="#" data-platform="telegram">
                                                <i class="fab fa-telegram me-2 text-primary"></i>تيليجرام
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item share-btn" href="#" data-platform="copy">
                                                <i class="fas fa-copy me-2 text-secondary"></i>نسخ الرابط
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 text-md-end">
                                <!-- Print and Save Buttons -->
                                <div class="d-flex align-items-center justify-content-md-end gap-2">
                                    <button class="btn btn-modern-secondary"
                                            onclick="window.print()"
                                            style="border-radius: var(--radius-lg);">
                                        <i class="fas fa-print me-1"></i>طباعة
                                    </button>

                                    <button class="btn btn-modern-secondary bookmark-btn"
                                            data-article-id="{{ article.id }}"
                                            style="border-radius: var(--radius-lg);">
                                        <i class="fas fa-bookmark me-1"></i>حفظ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Article Tags -->
                    {% if article.tags.exists %}
                    <div class="article-tags mb-5">
                        <h5 class="mb-3 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                            <i class="fas fa-tags me-2 text-primary"></i>العلامات ذات الصلة
                        </h5>
                        <div class="d-flex flex-wrap gap-2">
                            {% for tag in article.tags.all %}
                                <a href="{% url 'articles:tag_detail' tag.slug %}"
                                   class="tag-modern">
                                    #{{ tag.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Author Bio Card -->
                    <div class="author-bio-card modern-card p-4 mb-5"
                         style="border-radius: var(--radius-xl); background: var(--gradient-primary); color: white;">
                        <div class="d-flex align-items-center">
                            <div class="author-avatar me-3"
                                 style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 2rem; font-weight: 600; border: 3px solid rgba(255,255,255,0.3);">
                                {{ article.author.get_full_name.0|default:article.author.username.0|upper }}
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="mb-1 text-white" style="font-weight: 600;">
                                    {{ article.author.get_full_name|default:article.author.username }}
                                </h5>
                                <p class="mb-2 text-white-50" style="font-size: 0.9rem;">
                                    {% if article.author.bio %}
                                        {{ article.author.bio|truncatewords:20 }}
                                    {% else %}
                                        كاتب في تاتا نيوز
                                    {% endif %}
                                </p>
                                <div class="author-stats d-flex gap-3 text-white-50" style="font-size: 0.875rem;">
                                    <span><i class="fas fa-newspaper me-1"></i>{{ article.author.articles.count }} مقال</span>
                                    <span><i class="fas fa-calendar me-1"></i>عضو منذ {{ article.author.date_joined|date:"Y" }}</span>
                                </div>
                            </div>
                            <div>
                                <a href="#" class="btn btn-light btn-sm" style="border-radius: var(--radius-lg);">
                                    <i class="fas fa-user-plus me-1"></i>متابعة
                                </a>
                            </div>
                        </div>
                    </div>
                </article>
            </div>

            <!-- Modern Sidebar -->
            <div class="col-lg-4">
                <div class="article-sidebar">
                    <!-- Table of Contents -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-list me-2 text-primary"></i>فهرس المحتويات
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            <div id="table-of-contents">
                                <!-- Will be populated by JavaScript -->
                                <p class="text-muted small">سيتم إنشاء فهرس المحتويات تلقائياً...</p>
                            </div>
                        </div>
                    </div>

                    <!-- Related Articles -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-newspaper me-2 text-success"></i>مقالات ذات صلة
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            {% for related_article in related_articles|slice:":4" %}
                            <div class="related-article-item d-flex mb-3 {% if not forloop.last %}pb-3 border-bottom border-light{% endif %}">
                                {% if related_article.featured_image %}
                                <div class="article-thumb me-3 flex-shrink-0">
                                    <img src="{{ related_article.featured_image.url }}"
                                         alt="{{ related_article.title }}"
                                         class="rounded hover-scale"
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: var(--radius-md);">
                                </div>
                                {% endif %}
                                <div class="article-info flex-grow-1">
                                    <h6 class="article-title mb-1" style="font-size: 0.9rem; line-height: 1.4;">
                                        <a href="{{ related_article.get_absolute_url }}"
                                           class="text-decoration-none text-dark hover-lift">
                                            {{ related_article.title|truncatewords:8 }}
                                        </a>
                                    </h6>
                                    <div class="article-meta small text-muted d-flex align-items-center">
                                        <span class="me-3">
                                            <i class="fas fa-calendar me-1"></i>{{ related_article.published_at|date:"d M" }}
                                        </span>
                                        <span>
                                            <i class="fas fa-eye me-1"></i>{{ related_article.views_count }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <p class="text-muted text-center">لا توجد مقالات ذات صلة</p>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Newsletter Widget -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl); background: var(--gradient-accent); color: white;">
                        <div class="widget-content p-4 text-center">
                            <i class="fas fa-envelope-open text-white mb-3" style="font-size: 2.5rem; opacity: 0.8;"></i>
                            <h5 class="widget-title mb-2 text-white" style="font-weight: 600;">
                                لا تفوت أي خبر!
                            </h5>
                            <p class="text-white-50 mb-3" style="font-size: 0.9rem;">
                                اشترك في نشرتنا البريدية واحصل على آخر الأخبار
                            </p>

                            <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <input type="email"
                                           class="modern-input w-100"
                                           name="email"
                                           placeholder="بريدك الإلكتروني"
                                           required
                                           style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white;">
                                </div>
                                <button class="btn btn-light w-100" type="submit" style="border-radius: var(--radius-md); font-weight: 500;">
                                    <i class="fas fa-paper-plane me-1"></i>اشتراك مجاني
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Popular Articles -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-fire me-2 text-danger"></i>الأكثر قراءة
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            {% for popular_article in popular_articles|slice:":5" %}
                            <div class="popular-article-item d-flex align-items-center mb-3 {% if not forloop.last %}pb-3 border-bottom border-light{% endif %}">
                                <div class="article-rank me-3 flex-shrink-0"
                                     style="width: 30px; height: 30px; background: var(--primary-100); color: var(--primary-700); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">
                                    {{ forloop.counter }}
                                </div>
                                <div class="article-info flex-grow-1">
                                    <h6 class="article-title mb-1" style="font-size: 0.875rem; line-height: 1.4;">
                                        <a href="{{ popular_article.get_absolute_url }}"
                                           class="text-decoration-none text-dark hover-lift">
                                            {{ popular_article.title|truncatewords:6 }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>{{ popular_article.views_count }} مشاهدة
                                    </small>
                                </div>
                            </div>
                            {% empty %}
                            <p class="text-muted text-center">لا توجد مقالات شائعة</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <!-- Modern Comments Section -->
    {% if article.allow_comments %}
    <section class="comments-section py-5" style="background: var(--neutral-50);">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="comments-container">
                        <!-- Comments Header -->
                        <div class="comments-header mb-4">
                            <h3 class="d-flex align-items-center mb-3" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-comments me-2 text-primary"></i>
                                التعليقات ({{ comments.count }})
                            </h3>
                        </div>

                        <!-- Add Comment Form -->
                        {% if user.is_authenticated %}
                        <div class="add-comment-form modern-card p-4 mb-5" style="border-radius: var(--radius-xl);">
                            <div class="d-flex align-items-start">
                                <div class="user-avatar me-3"
                                     style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; font-weight: 600;">
                                    {{ user.get_full_name.0|default:user.username.0|upper }}
                                </div>
                                <div class="flex-grow-1">
                                    <form method="POST" action="{% url 'comments:add' article.id %}" class="comment-form">
                                        {% csrf_token %}
                                        <div class="form-group-modern mb-3">
                                            <textarea name="content"
                                                      class="form-input-modern"
                                                      rows="4"
                                                      placeholder="شاركنا رأيك في هذا المقال..."
                                                      required
                                                      style="resize: vertical; min-height: 100px;"></textarea>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                سيتم مراجعة تعليقك قبل النشر
                                            </small>
                                            <button type="submit" class="btn btn-modern-primary">
                                                <i class="fas fa-paper-plane me-1"></i>إرسال التعليق
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="login-prompt modern-card p-4 mb-5 text-center"
                             style="border-radius: var(--radius-xl); background: var(--primary-50); border: 1px solid var(--primary-200);">
                            <i class="fas fa-user-circle text-primary mb-2" style="font-size: 2rem;"></i>
                            <h5 class="mb-2">انضم إلى النقاش</h5>
                            <p class="text-muted mb-3">سجل الدخول لإضافة تعليقك ومشاركة رأيك</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="{% url 'account_login' %}" class="btn btn-modern-primary">
                                    <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                                </a>
                                <a href="{% url 'account_signup' %}" class="btn btn-modern-secondary">
                                    <i class="fas fa-user-plus me-1"></i>إنشاء حساب
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Comments List -->
                        <div class="comments-list">
                            {% for comment in comments %}
                            <div class="comment-item modern-card p-4 mb-4"
                                 id="comment-{{ comment.id }}"
                                 style="border-radius: var(--radius-xl);">
                                <div class="d-flex align-items-start">
                                    <!-- Comment Author Avatar -->
                                    <div class="comment-avatar me-3"
                                         style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; font-weight: 600;">
                                        {{ comment.author.get_full_name.0|default:comment.author.username.0|upper }}
                                    </div>

                                    <div class="comment-body flex-grow-1">
                                        <!-- Comment Header -->
                                        <div class="comment-header d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="comment-author mb-1" style="font-weight: 600; color: var(--neutral-900);">
                                                    {{ comment.author.get_full_name|default:comment.author.username }}
                                                </h6>
                                                <small class="comment-date text-muted d-flex align-items-center">
                                                    <i class="fas fa-clock me-1"></i>
                                                    {{ comment.created_at|date:"d M Y - H:i" }}
                                                </small>
                                            </div>

                                            <!-- Comment Actions -->
                                            <div class="comment-actions d-flex gap-1">
                                                {% if user.is_authenticated %}
                                                    <button class="btn btn-sm btn-outline-primary comment-like-btn"
                                                            data-comment-id="{{ comment.id }}"
                                                            style="border-radius: var(--radius-md);">
                                                        <i class="far fa-thumbs-up"></i>
                                                        <span class="like-count">{{ comment.likes_count }}</span>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-secondary reply-btn"
                                                            data-comment-id="{{ comment.id }}"
                                                            style="border-radius: var(--radius-md);">
                                                        <i class="fas fa-reply"></i>
                                                    </button>
                                                {% endif %}
                                                {% if user == comment.author %}
                                                    <button class="btn btn-sm btn-outline-warning"
                                                            style="border-radius: var(--radius-md);">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            style="border-radius: var(--radius-md);">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Comment Content -->
                                        <div class="comment-content" style="line-height: 1.6; color: var(--neutral-700);">
                                            {{ comment.content|linebreaks }}
                                        </div>

                                        <!-- Reply Form -->
                                        {% if user.is_authenticated %}
                                        <div class="reply-form mt-3" id="reply-form-{{ comment.id }}" style="display: none;">
                                            <div class="d-flex align-items-start">
                                                <div class="user-avatar me-2"
                                                     style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1rem; font-weight: 600;">
                                                    {{ user.get_full_name.0|default:user.username.0|upper }}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <form method="POST" action="{% url 'comments:reply' comment.id %}">
                                                        {% csrf_token %}
                                                        <div class="mb-2">
                                                            <textarea name="content"
                                                                      class="form-input-modern"
                                                                      rows="3"
                                                                      placeholder="اكتب ردك هنا..."
                                                                      required
                                                                      style="font-size: 0.9rem;"></textarea>
                                                        </div>
                                                        <div class="d-flex gap-2">
                                                            <button type="submit" class="btn btn-modern-primary btn-sm">
                                                                <i class="fas fa-reply me-1"></i>إرسال الرد
                                                            </button>
                                                            <button type="button"
                                                                    class="btn btn-modern-secondary btn-sm"
                                                                    onclick="this.closest('.reply-form').style.display='none'">
                                                                إلغاء
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}

                                        <!-- Replies -->
                                        {% for reply in comment.get_replies %}
                                        <div class="comment-reply mt-3 p-3"
                                             style="background: var(--neutral-50); border-radius: var(--radius-lg); border-right: 3px solid var(--primary-500);">
                                            <div class="d-flex align-items-start">
                                                <div class="reply-avatar me-2"
                                                     style="width: 40px; height: 40px; background: var(--gradient-accent); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1rem; font-weight: 600;">
                                                    {{ reply.author.get_full_name.0|default:reply.author.username.0|upper }}
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="reply-header mb-1">
                                                        <h6 class="reply-author mb-0" style="font-weight: 600; color: var(--neutral-900); font-size: 0.9rem;">
                                                            {{ reply.author.get_full_name|default:reply.author.username }}
                                                        </h6>
                                                        <small class="reply-date text-muted">
                                                            <i class="fas fa-clock me-1"></i>
                                                            {{ reply.created_at|date:"d M Y - H:i" }}
                                                        </small>
                                                    </div>
                                                    <div class="reply-content" style="line-height: 1.5; color: var(--neutral-700); font-size: 0.9rem;">
                                                        {{ reply.content|linebreaks }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="no-comments text-center py-5">
                                <i class="fas fa-comments text-muted mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                                <h5 class="text-muted mb-2">لا توجد تعليقات بعد</h5>
                                <p class="text-muted">كن أول من يعلق على هذا المقال</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Article specific styles */
    .modern-article-content .content-body h2,
    .modern-article-content .content-body h3,
    .modern-article-content .content-body h4 {
        margin-top: 2rem;
        margin-bottom: 1rem;
        color: var(--neutral-900);
    }

    .modern-article-content .content-body p {
        margin-bottom: 1.5rem;
        text-align: justify;
    }

    .modern-article-content .content-body img {
        max-width: 100%;
        height: auto;
        border-radius: var(--radius-lg);
        margin: 1.5rem 0;
    }

    .modern-article-content .content-body blockquote {
        border-right: 4px solid var(--primary-500);
        background: var(--primary-50);
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        border-radius: var(--radius-md);
        font-style: italic;
    }

    .modern-article-content .content-body ul,
    .modern-article-content .content-body ol {
        padding-right: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .modern-article-content .content-body li {
        margin-bottom: 0.5rem;
    }

    /* Comment animations */
    .comment-item {
        animation: slideInUp 0.5s ease-out;
    }

    .reply-form {
        animation: slideInDown 0.3s ease-out;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .article-title {
            font-size: 2rem !important;
        }

        .article-subtitle {
            font-size: 1.125rem !important;
        }

        .article-meta {
            flex-direction: column;
            gap: 1rem;
        }

        .article-stats {
            justify-content: center !important;
        }

        .comment-actions {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize article features
    initArticleFeatures();

    function initArticleFeatures() {
        generateTableOfContents();
        initReadingProgress();
        initCommentFeatures();
        initShareButtons();
        initLikeButton();
    }

    function generateTableOfContents() {
        const content = document.querySelector('.content-body');
        const toc = document.getElementById('table-of-contents');

        if (!content || !toc) return;

        const headings = content.querySelectorAll('h2, h3, h4');

        if (headings.length === 0) {
            toc.innerHTML = '<p class="text-muted small">لا توجد عناوين فرعية في هذا المقال</p>';
            return;
        }

        let tocHTML = '<ul class="list-unstyled">';
        headings.forEach((heading, index) => {
            const id = `heading-${index}`;
            heading.id = id;
            const level = parseInt(heading.tagName.charAt(1));
            const indent = level > 2 ? 'ps-3' : '';

            tocHTML += `
                <li class="${indent} mb-1">
                    <a href="#${id}" class="text-decoration-none d-block p-2 rounded hover-lift"
                       style="font-size: 0.875rem; transition: all var(--transition-fast);">
                        ${heading.textContent}
                    </a>
                </li>
            `;
        });
        tocHTML += '</ul>';

        toc.innerHTML = tocHTML;

        // Add smooth scroll behavior
        toc.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });
    }

    function initReadingProgress() {
        const progressBar = document.getElementById('reading-progress');
        if (!progressBar) return;

        function updateProgress() {
            const article = document.querySelector('.content-body');
            if (!article) return;

            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;

            const progress = Math.min(
                Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
                1
            );

            progressBar.style.width = `${progress * 100}%`;
        }

        window.addEventListener('scroll', updateProgress);
        updateProgress();
    }

    function initCommentFeatures() {
        // Reply button functionality
        document.querySelectorAll('.reply-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const commentId = this.dataset.commentId;
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                if (replyForm) {
                    replyForm.style.display = replyForm.style.display === 'none' ? 'block' : 'none';
                }
            });
        });

        // Comment like functionality
        document.querySelectorAll('.comment-like-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const commentId = this.dataset.commentId;
                // Implement comment like functionality
                console.log('Like comment:', commentId);
            });
        });
    }

    function initShareButtons() {
        document.querySelectorAll('.share-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const platform = this.dataset.platform;
                const url = encodeURIComponent(window.location.href);
                const title = encodeURIComponent(document.title);

                shareArticle(platform, url, title);
            });
        });
    }

    function initLikeButton() {
        const likeBtn = document.querySelector('.like-btn');
        if (likeBtn) {
            likeBtn.addEventListener('click', function() {
                const articleId = this.dataset.articleId;
                // Implement article like functionality
                console.log('Like article:', articleId);
            });
        }
    }

    function shareArticle(platform, url, title) {
        let shareUrl = '';

        switch (platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                break;
            case 'whatsapp':
                shareUrl = `https://wa.me/?text=${title} ${url}`;
                break;
            case 'telegram':
                shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
                break;
            case 'copy':
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('تم نسخ الرابط إلى الحافظة!', 'success');
                });
                return;
        }

        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }

    function showNotification(message, type = 'info') {
        // Use the modern notification system from modern.js
        if (window.ModernForms) {
            const modernForms = new window.ModernForms();
            modernForms.showNotification(message, type);
        }
    }
});
</script>
{% endblock %}