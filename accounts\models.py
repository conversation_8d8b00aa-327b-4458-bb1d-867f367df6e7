from django.contrib.auth.models import AbstractUser
from django.db import models
from PIL import Image

class CustomUser(AbstractUser):
    """نموذج المستخدم المخصص"""
    
    ROLE_CHOICES = [
        ('admin', 'مدير'),
        ('editor', 'محرر'),
        ('author', 'كاتب'),
        ('subscriber', 'مشترك'),
    ]
    
    email = models.EmailField(unique=True, verbose_name='البريد الإلكتروني')
    first_name = models.CharField(max_length=30, verbose_name='الاسم الأول')
    last_name = models.CharField(max_length=30, verbose_name='اسم العائلة')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='subscriber', verbose_name='الدور')
    bio = models.TextField(max_length=500, blank=True, verbose_name='نبذة شخصية')
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True, verbose_name='الصورة الشخصية')
    phone = models.CharField(max_length=20, blank=True, verbose_name='رقم الهاتف')
    birth_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الميلاد')
    is_verified = models.BooleanField(default=False, verbose_name='مُتحقق منه')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']
    
    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.first_name} {self.last_name}"
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        
        # تصغير حجم الصورة الشخصية
        if self.avatar:
            img = Image.open(self.avatar.path)
            if img.height > 300 or img.width > 300:
                output_size = (300, 300)
                img.thumbnail(output_size)
                img.save(self.avatar.path)
    
    @property
    def can_publish(self):
        """تحديد ما إذا كان المستخدم يمكنه النشر"""
        return self.role in ['admin', 'editor', 'author']
    
    @property
    def can_moderate(self):
        """تحديد ما إذا كان المستخدم يمكنه الإشراف"""
        return self.role in ['admin', 'editor']
