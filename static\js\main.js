// تاتا نيوز - JavaScript الرئيسي

document.addEventListener('DOMContentLoaded', function() {
    
    // Back to Top Button
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Search form enhancement
    const searchForm = document.querySelector('.search-form');
    const searchInput = searchForm?.querySelector('input[name="q"]');
    
    if (searchInput) {
        // Add search suggestions (you can implement this with AJAX)
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                // Implement search suggestions here
                // fetchSearchSuggestions(query);
            }
        });
    }
    
    // Article like functionality
    const likeButtons = document.querySelectorAll('.like-btn');
    likeButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const articleId = this.dataset.articleId;
            toggleLike(articleId, this);
        });
    });
    
    // Comment like functionality
    const commentLikeButtons = document.querySelectorAll('.comment-like-btn');
    commentLikeButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const commentId = this.dataset.commentId;
            toggleCommentLike(commentId, this);
        });
    });
    
    // Newsletter subscription
    const newsletterForms = document.querySelectorAll('form[action*="newsletter"]');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            subscribeToNewsletter(this);
        });
    });
    
    // Image lazy loading
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Reading progress bar
    const progressBar = document.querySelector('.reading-progress');
    if (progressBar) {
        window.addEventListener('scroll', updateReadingProgress);
    }
    
    // Share buttons
    const shareButtons = document.querySelectorAll('.share-btn');
    shareButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const platform = this.dataset.platform;
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent(document.title);
            
            shareArticle(platform, url, title);
        });
    });
    
    // Comment form enhancement
    const commentForms = document.querySelectorAll('.comment-form');
    commentForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitComment(this);
        });
    });
    
    // Reply to comment
    const replyButtons = document.querySelectorAll('.reply-btn');
    replyButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const commentId = this.dataset.commentId;
            showReplyForm(commentId);
        });
    });
});

// Article like toggle
function toggleLike(articleId, button) {
    const isLiked = button.classList.contains('liked');
    const icon = button.querySelector('i');
    const countSpan = button.querySelector('.like-count');
    
    // Show loading state
    button.disabled = true;
    icon.className = 'fas fa-spinner fa-spin';
    
    fetch(`/articles/${articleId}/like/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.liked) {
                button.classList.add('liked');
                icon.className = 'fas fa-heart text-danger';
            } else {
                button.classList.remove('liked');
                icon.className = 'far fa-heart';
            }
            countSpan.textContent = data.likes_count;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء تسجيل الإعجاب', 'error');
    })
    .finally(() => {
        button.disabled = false;
    });
}

// Comment like toggle
function toggleCommentLike(commentId, button) {
    const icon = button.querySelector('i');
    const countSpan = button.querySelector('.like-count');
    
    button.disabled = true;
    icon.className = 'fas fa-spinner fa-spin';
    
    fetch(`/comments/${commentId}/like/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.liked) {
                button.classList.add('liked');
                icon.className = 'fas fa-thumbs-up text-primary';
            } else {
                button.classList.remove('liked');
                icon.className = 'far fa-thumbs-up';
            }
            countSpan.textContent = data.likes_count;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء تسجيل الإعجاب', 'error');
    })
    .finally(() => {
        button.disabled = false;
    });
}

// Newsletter subscription
function subscribeToNewsletter(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading"></span> جاري الاشتراك...';
    
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم الاشتراك بنجاح في النشرة البريدية!', 'success');
            form.reset();
        } else {
            showNotification(data.message || 'حدث خطأ أثناء الاشتراك', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء الاشتراك', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// Submit comment
function submitComment(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading"></span> جاري الإرسال...';
    
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إرسال التعليق بنجاح! سيتم مراجعته قبل النشر.', 'success');
            form.reset();
            // Optionally reload comments section
            if (data.html) {
                document.querySelector('.comments-section').innerHTML = data.html;
            }
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إرسال التعليق', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إرسال التعليق', 'error');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// Show reply form
function showReplyForm(commentId) {
    const replyForm = document.querySelector(`#reply-form-${commentId}`);
    if (replyForm) {
        replyForm.style.display = replyForm.style.display === 'none' ? 'block' : 'none';
    }
}

// Share article
function shareArticle(platform, url, title) {
    let shareUrl = '';
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
            break;
        case 'linkedin':
            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
            break;
        case 'whatsapp':
            shareUrl = `https://wa.me/?text=${title} ${url}`;
            break;
        case 'telegram':
            shareUrl = `https://t.me/share/url?url=${url}&text=${title}`;
            break;
        default:
            // Copy to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('تم نسخ الرابط إلى الحافظة!', 'success');
            });
            return;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

// Update reading progress
function updateReadingProgress() {
    const article = document.querySelector('.article-content');
    if (!article) return;
    
    const articleTop = article.offsetTop;
    const articleHeight = article.offsetHeight;
    const windowHeight = window.innerHeight;
    const scrollTop = window.pageYOffset;
    
    const progress = Math.min(
        Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
        1
    );
    
    const progressBar = document.querySelector('.reading-progress');
    if (progressBar) {
        progressBar.style.width = `${progress * 100}%`;
    }
}

// Show notification
function showNotification(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' : 
                     type === 'success' ? 'alert-success' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; left: 20px; z-index: 9999; min-width: 300px;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert:last-of-type');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Infinite scroll for article lists
function initInfiniteScroll() {
    let loading = false;
    let page = 2;
    
    window.addEventListener('scroll', () => {
        if (loading) return;
        
        if (window.innerHeight + window.scrollY >= document.body.offsetHeight - 1000) {
            loading = true;
            loadMoreArticles(page);
        }
    });
}

// Load more articles
function loadMoreArticles(page) {
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    
    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.html) {
            document.querySelector('.articles-container').insertAdjacentHTML('beforeend', data.html);
            page++;
            
            if (!data.has_next) {
                // No more articles to load
                const loadingIndicator = document.querySelector('.loading-indicator');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            }
        }
    })
    .catch(error => {
        console.error('Error loading more articles:', error);
    })
    .finally(() => {
        loading = false;
    });
}

// Initialize features based on page
function initPageFeatures() {
    const currentPage = document.body.dataset.page;
    
    switch (currentPage) {
        case 'article-list':
            initInfiniteScroll();
            break;
        case 'article-detail':
            // Initialize article-specific features
            break;
    }
}

// Call initialization
initPageFeatures();