from django import forms
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit
from crispy_forms.bootstrap import FormActions
from .models import Comment

class CommentForm(forms.ModelForm):
    """نموذج التعليقات"""
    
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'اكتب تعليقك هنا...',
                'required': True
            })
        }
        labels = {
            'content': 'التعليق'
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'content',
            FormActions(
                Submit('submit', 'إرسال التعليق', css_class='btn btn-primary'),
            )
        )
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        
        if len(content) < 10:
            raise forms.ValidationError('التعليق قصير جداً (الحد الأدنى 10 أحرف)')
        
        if len(content) > 1000:
            raise forms.ValidationError('التعليق طويل جداً (الحد الأقصى 1000 حرف)')
        
        # فلترة الكلمات غير المناسبة
        inappropriate_words = ['spam', 'fake']  # يمكن توسيعها
        for word in inappropriate_words:
            if word.lower() in content.lower():
                raise forms.ValidationError('التعليق يحتوي على كلمات غير مناسبة')
        
        return content

class ReplyForm(CommentForm):
    """نموذج الرد على التعليقات"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'content',
            FormActions(
                Submit('submit', 'إرسال الرد', css_class='btn btn-primary btn-sm'),
            )
        )