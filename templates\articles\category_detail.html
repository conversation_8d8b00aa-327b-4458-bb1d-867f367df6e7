{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ category.name }} - تاتا نيوز{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'articles:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item active">{{ category.name }}</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title">
                    {% if category.icon %}
                        <i class="{{ category.icon }}" style="color: {{ category.color }};"></i>
                    {% endif %}
                    {{ category.name }}
                </h2>
                {% if page_obj.paginator.count %}
                    <span class="badge" style="background-color: {{ category.color }};">{{ page_obj.paginator.count }} مقال</span>
                {% endif %}
            </div>
            
            {% if category.description %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                {{ category.description }}
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="row">
        <!-- Articles -->
        <div class="col-lg-8">
            {% if page_obj %}
                <div class="row">
                    {% for article in articles %}
                    <div class="col-md-6 mb-4">
                        <div class="card article-card h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                        {{ article.title }}
                                    </a>
                                </h5>
                                <p class="card-text text-muted">{{ article.excerpt|truncatewords:20 }}</p>
                                <small class="text-muted">{{ article.published_at|date:"d M Y" }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="تنقل الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <h4>لا توجد مقالات في هذه الفئة</h4>
                    <p class="text-muted">لم يتم نشر أي مقالات في فئة "{{ category.name }}" بعد.</p>
                    <a href="{% url 'articles:home' %}" class="btn btn-primary mt-3">العودة للرئيسية</a>
                </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sidebar">
                <h5>فئات أخرى</h5>
                {% for cat in categories %}
                    {% if cat != category %}
                    <p><a href="{{ cat.get_absolute_url }}">{{ cat.name }}</a></p>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}