{% load static %}

<!-- Modern Navigation Component -->
<nav class="modern-navigation-enhanced" id="mainNavigation">
    <!-- Top Bar -->
    <div class="top-bar-enhanced" style="background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%); color: white; padding: 0.5rem 0;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <!-- Live Date & Time -->
                        <div class="live-datetime d-flex align-items-center me-4">
                            <i class="fas fa-calendar-alt me-2 text-primary-200"></i>
                            <span id="live-date" class="text-sm"></span>
                            <span class="mx-2 text-primary-200">|</span>
                            <i class="fas fa-clock me-1 text-primary-200"></i>
                            <span id="live-time" class="text-sm"></span>
                        </div>
                        
                        <!-- Weather Widget -->
                        <div class="weather-widget d-none d-lg-flex align-items-center">
                            <i class="fas fa-thermometer-half me-1 text-primary-200"></i>
                            <span class="text-sm" id="weather-info">القاهرة 25°</span>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 text-end">
                    <div class="d-flex align-items-center justify-content-end gap-3">
                        <!-- Language Switcher -->
                        <div class="language-switcher d-none d-md-block">
                            <button class="btn btn-sm btn-outline-light border-0" title="تغيير اللغة">
                                <i class="fas fa-globe me-1"></i>
                                <span class="small">العربية</span>
                            </button>
                        </div>
                        
                        <!-- Theme Toggle -->
                        <button id="theme-toggle-enhanced" class="btn btn-sm btn-outline-light border-0" title="تغيير المظهر">
                            <i class="fas fa-moon" id="theme-icon-enhanced"></i>
                        </button>
                        
                        <!-- Notifications -->
                        <div class="notifications-dropdown d-none d-md-block">
                            <button class="btn btn-sm btn-outline-light border-0 position-relative" 
                                    data-bs-toggle="dropdown" 
                                    title="الإشعارات">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" 
                                      style="font-size: 0.6rem;">3</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end modern-card border-0 mt-2" style="min-width: 300px;">
                                <li class="dropdown-header d-flex justify-content-between align-items-center">
                                    <span>الإشعارات</span>
                                    <small class="text-muted">3 جديدة</small>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item d-flex align-items-start p-3" href="#">
                                    <i class="fas fa-newspaper text-primary me-2 mt-1"></i>
                                    <div>
                                        <div class="fw-semibold">مقال جديد</div>
                                        <small class="text-muted">تم نشر مقال جديد في قسم التكنولوجيا</small>
                                        <small class="text-muted d-block">منذ 5 دقائق</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item d-flex align-items-start p-3" href="#">
                                    <i class="fas fa-comment text-success me-2 mt-1"></i>
                                    <div>
                                        <div class="fw-semibold">تعليق جديد</div>
                                        <small class="text-muted">أحمد علق على مقالك الأخير</small>
                                        <small class="text-muted d-block">منذ 10 دقائق</small>
                                    </div>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
                            </ul>
                        </div>
                        
                        <!-- Social Links -->
                        <div class="social-links d-flex gap-2">
                            <a href="#" class="text-white hover-scale" title="فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="text-white hover-scale" title="تويتر">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="text-white hover-scale" title="إنستغرام">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="text-white hover-scale" title="يوتيوب">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="text-white hover-scale" title="تيليجرام">
                                <i class="fab fa-telegram"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Header -->
    <div class="main-header-enhanced py-3" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-bottom: 1px solid var(--neutral-200);">
        <div class="container">
            <div class="row align-items-center">
                <!-- Logo Section -->
                <div class="col-lg-3 col-md-4">
                    <div class="logo-enhanced">
                        <a href="{% url 'articles:home' %}" class="text-decoration-none d-flex align-items-center">
                            <div class="logo-icon me-3 position-relative">
                                <div class="d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px; background: var(--gradient-primary); border-radius: var(--radius-xl); color: white; font-size: 1.75rem; font-weight: bold; box-shadow: var(--shadow-lg);">
                                    ت
                                    <div class="logo-pulse position-absolute top-0 start-0 w-100 h-100 rounded" 
                                         style="background: var(--gradient-primary); opacity: 0.3; animation: pulse 2s infinite;"></div>
                                </div>
                            </div>
                            <div>
                                <h1 class="mb-0 fw-bold" style="font-size: 2rem; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                                    تاتا نيوز
                                </h1>
                                <small class="text-muted d-block" style="font-size: 0.8rem; letter-spacing: 0.5px;">
                                    جريدة إلكترونية عصرية
                                </small>
                            </div>
                        </a>
                    </div>
                </div>
                
                <!-- Enhanced Search Section -->
                <div class="col-lg-6 col-md-5">
                    <div class="search-enhanced position-relative">
                        <form class="search-form-enhanced" method="GET" action="{% url 'articles:search' %}">
                            <div class="search-container position-relative">
                                <input type="text" 
                                       class="search-input-enhanced w-100" 
                                       name="q" 
                                       placeholder="ابحث في الأخبار والمقالات..." 
                                       value="{{ request.GET.q }}"
                                       autocomplete="off"
                                       style="padding: 0.75rem 3.5rem 0.75rem 1rem; border: 2px solid var(--neutral-300); border-radius: var(--radius-xl); font-size: 1rem; transition: all var(--transition-fast);">
                                
                                <button class="search-btn position-absolute top-50 end-0 translate-middle-y me-3 border-0 bg-transparent" 
                                        type="submit"
                                        style="color: var(--primary-600);">
                                    <i class="fas fa-search"></i>
                                </button>
                                
                                <button class="voice-search-btn position-absolute top-50 start-0 translate-middle-y ms-3 border-0 bg-transparent d-none d-md-block" 
                                        type="button"
                                        title="البحث الصوتي"
                                        style="color: var(--neutral-500);">
                                    <i class="fas fa-microphone"></i>
                                </button>
                                
                                <!-- Search Suggestions -->
                                <div class="search-suggestions position-absolute top-100 start-0 w-100 mt-2 modern-card border-0 d-none" 
                                     style="z-index: 1000; max-height: 300px; overflow-y: auto;">
                                    <div class="suggestions-content p-3">
                                        <div class="suggestions-header mb-2">
                                            <small class="text-muted fw-semibold">اقتراحات البحث</small>
                                        </div>
                                        <div class="suggestions-list">
                                            <!-- Will be populated by JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- User Section -->
                <div class="col-lg-3 col-md-3 text-end">
                    {% if user.is_authenticated %}
                        <div class="user-menu-enhanced">
                            <div class="dropdown">
                                <button class="btn btn-modern-secondary dropdown-toggle border-0 d-flex align-items-center" 
                                        type="button" 
                                        data-bs-toggle="dropdown" 
                                        aria-expanded="false"
                                        style="border-radius: var(--radius-xl);">
                                    <div class="user-avatar-enhanced me-2" 
                                         style="width: 40px; height: 40px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1rem; font-weight: 600;">
                                        {{ user.get_full_name.0|default:user.username.0|upper }}
                                    </div>
                                    <div class="user-info d-none d-lg-block text-start">
                                        <div class="user-name" style="font-size: 0.9rem; font-weight: 600;">
                                            {{ user.get_full_name|default:user.username|truncatechars:15 }}
                                        </div>
                                        <small class="text-muted">{{ user.email|truncatechars:20 }}</small>
                                    </div>
                                </button>
                                
                                <ul class="dropdown-menu dropdown-menu-end modern-card border-0 mt-2" style="min-width: 250px;">
                                    <li class="dropdown-header d-flex align-items-center p-3">
                                        <div class="user-avatar-enhanced me-2" 
                                             style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; font-weight: 600;">
                                            {{ user.get_full_name.0|default:user.username.0|upper }}
                                        </div>
                                        <div>
                                            <div class="fw-semibold">{{ user.get_full_name|default:user.username }}</div>
                                            <small class="text-muted">{{ user.email }}</small>
                                        </div>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item d-flex align-items-center p-3" href="{% url 'accounts:profile' %}">
                                        <i class="fas fa-user me-2 text-primary"></i>الملف الشخصي
                                    </a></li>
                                    <li><a class="dropdown-item d-flex align-items-center p-3" href="#">
                                        <i class="fas fa-bookmark me-2 text-warning"></i>المقالات المحفوظة
                                    </a></li>
                                    <li><a class="dropdown-item d-flex align-items-center p-3" href="#">
                                        <i class="fas fa-cog me-2 text-secondary"></i>الإعدادات
                                    </a></li>
                                    {% if user.can_publish %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item d-flex align-items-center p-3" href="{% url 'articles:create' %}">
                                            <i class="fas fa-plus me-2 text-success"></i>إضافة مقال
                                        </a></li>
                                        <li><a class="dropdown-item d-flex align-items-center p-3" href="#">
                                            <i class="fas fa-chart-line me-2 text-info"></i>إحصائياتي
                                        </a></li>
                                    {% endif %}
                                    {% if user.is_staff %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item d-flex align-items-center p-3" href="/admin/">
                                            <i class="fas fa-shield-alt me-2 text-danger"></i>لوحة الإدارة
                                        </a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item d-flex align-items-center p-3 text-danger" href="{% url 'account_logout' %}">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    {% else %}
                        <div class="auth-buttons d-flex gap-2">
                            <a href="{% url 'account_login' %}" class="btn btn-modern-secondary">
                                <i class="fas fa-sign-in-alt me-1"></i>دخول
                            </a>
                            <a href="{% url 'account_signup' %}" class="btn btn-modern-primary">
                                <i class="fas fa-user-plus me-1"></i>تسجيل
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Enhanced Navigation Menu -->
    <div class="navigation-menu-enhanced" style="background: var(--gradient-primary); border-bottom: 1px solid rgba(255,255,255,0.1);">
        <div class="container">
            <div class="d-flex align-items-center justify-content-between py-2">
                <!-- Mobile Menu Toggle -->
                <button class="mobile-menu-toggle btn btn-link text-white d-lg-none border-0 p-0" 
                        type="button" 
                        data-bs-toggle="collapse" 
                        data-bs-target="#mobileNavMenu"
                        aria-controls="mobileNavMenu" 
                        aria-expanded="false">
                    <div class="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </button>
                
                <!-- Desktop Navigation -->
                <ul class="nav-menu-desktop d-none d-lg-flex align-items-center mb-0 list-unstyled">
                    <li class="nav-item-enhanced me-1">
                        <a class="nav-link-enhanced text-white px-3 py-2 rounded hover-lift d-flex align-items-center" 
                           href="{% url 'articles:home' %}">
                            <i class="fas fa-home me-2"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item-enhanced dropdown me-1">
                        <a class="nav-link-enhanced dropdown-toggle text-white px-3 py-2 rounded hover-lift d-flex align-items-center" 
                           href="#" 
                           role="button" 
                           data-bs-toggle="dropdown"
                           aria-expanded="false">
                            <i class="fas fa-list me-2"></i>الأقسام
                        </a>
                        <ul class="dropdown-menu modern-card border-0 mt-2">
                            {% for category in categories %}
                                <li>
                                    <a class="dropdown-item d-flex align-items-center hover-lift p-3" 
                                       href="{{ category.get_absolute_url }}">
                                        {% if category.icon %}
                                            <i class="{{ category.icon }} me-2 text-primary"></i>
                                        {% else %}
                                            <i class="fas fa-folder me-2 text-primary"></i>
                                        {% endif %}
                                        {{ category.name }}
                                        <small class="text-muted ms-auto">{{ category.articles.count }}</small>
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                    </li>
                    <li class="nav-item-enhanced me-1">
                        <a class="nav-link-enhanced text-white px-3 py-2 rounded hover-lift d-flex align-items-center" 
                           href="{% url 'articles:latest' %}">
                            <i class="fas fa-clock me-2"></i>آخر الأخبار
                        </a>
                    </li>
                    <li class="nav-item-enhanced me-1">
                        <a class="nav-link-enhanced text-white px-3 py-2 rounded hover-lift d-flex align-items-center" 
                           href="{% url 'articles:featured' %}">
                            <i class="fas fa-star me-2"></i>المميزة
                        </a>
                    </li>
                    <li class="nav-item-enhanced me-1">
                        <a class="nav-link-enhanced text-white px-3 py-2 rounded hover-lift d-flex align-items-center" 
                           href="{% url 'newsletter:subscribe' %}">
                            <i class="fas fa-envelope me-2"></i>النشرة
                        </a>
                    </li>
                </ul>
                
                <!-- Breaking News Ticker -->
                {% if breaking_news %}
                    <div class="breaking-news-ticker-enhanced d-none d-xl-flex align-items-center" style="max-width: 400px;">
                        <span class="ticker-label badge bg-danger me-3 px-3 py-2" 
                              style="border-radius: var(--radius-lg); animation: pulse 2s infinite;">
                            <i class="fas fa-bolt me-1"></i>عاجل
                        </span>
                        <div class="ticker-content text-white overflow-hidden">
                            <div class="ticker-scroll">
                                {% for news in breaking_news|slice:":3" %}
                                    <a href="{{ news.get_absolute_url }}" 
                                       class="ticker-item text-white text-decoration-none me-4 d-inline-block">
                                        {{ news.title }}
                                    </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
            
            <!-- Mobile Navigation -->
            <div class="collapse" id="mobileNavMenu">
                <div class="mobile-nav-content py-3 border-top border-white border-opacity-25">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white py-2 d-flex align-items-center" href="{% url 'articles:home' %}">
                                <i class="fas fa-home me-3"></i>الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white py-2 d-flex align-items-center" href="{% url 'articles:latest' %}">
                                <i class="fas fa-clock me-3"></i>آخر الأخبار
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white py-2 d-flex align-items-center" href="{% url 'articles:featured' %}">
                                <i class="fas fa-star me-3"></i>المقالات المميزة
                            </a>
                        </li>
                        {% for category in categories|slice:":5" %}
                            <li class="nav-item">
                                <a class="nav-link text-white py-2 d-flex align-items-center ps-4" 
                                   href="{{ category.get_absolute_url }}">
                                    {% if category.icon %}
                                        <i class="{{ category.icon }} me-3"></i>
                                    {% else %}
                                        <i class="fas fa-folder me-3"></i>
                                    {% endif %}
                                    {{ category.name }}
                                </a>
                            </li>
                        {% endfor %}
                        <li class="nav-item">
                            <a class="nav-link text-white py-2 d-flex align-items-center" href="{% url 'newsletter:subscribe' %}">
                                <i class="fas fa-envelope me-3"></i>النشرة البريدية
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Breaking News Alert (Mobile) -->
{% if breaking_news %}
    <div class="breaking-news-mobile-enhanced d-xl-none alert alert-danger mb-0 rounded-0 border-0" 
         style="background: var(--accent-red);">
        <div class="container">
            <div class="d-flex align-items-center">
                <span class="badge bg-white text-danger me-2 px-2 py-1" style="border-radius: var(--radius-md);">
                    <i class="fas fa-bolt"></i>
                </span>
                <div class="flex-grow-1">
                    <div class="mobile-ticker text-white">
                        {% for news in breaking_news|slice:":3" %}
                            <a href="{{ news.get_absolute_url }}" 
                               class="mobile-ticker-item text-white text-decoration-none me-4">
                                {{ news.title }}
                            </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
