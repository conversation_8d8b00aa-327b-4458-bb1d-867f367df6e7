"""
دوال مساعدة لتطبيق المقالات
"""
import re
from django.utils.text import slugify
from unidecode import unidecode

def arabic_slugify(text):
    """
    إنشاء slug يدعم الأحرف العربية
    """
    if not text:
        return ''
    
    # تنظيف النص
    text = text.strip()
    
    # استبدال المسافات بشرطات
    text = re.sub(r'\s+', '-', text)
    
    # إزالة الأحرف الخاصة غير المرغوب فيها
    text = re.sub(r'[^\w\s\u0600-\u06FF\-]', '', text)
    
    # استخدام slugify مع دعم Unicode
    slug = slugify(text, allow_unicode=True)
    
    return slug

def get_client_ip(request):
    """
    الحصول على IP الخاص بالمستخدم
    """
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def increment_article_views(article, request):
    """
    زيادة عدد المشاهدات للمقال
    """
    from .models import ArticleView
    
    ip_address = get_client_ip(request)
    user = request.user if request.user.is_authenticated else None
    
    # التحقق من عدم وجود مشاهدة سابقة من نفس IP في آخر 24 ساعة
    from django.utils import timezone
    from datetime import timedelta
    
    yesterday = timezone.now() - timedelta(days=1)
    
    existing_view = ArticleView.objects.filter(
        article=article,
        ip_address=ip_address,
        created_at__gte=yesterday
    ).first()
    
    if not existing_view:
        ArticleView.objects.create(
            article=article,
            user=user,
            ip_address=ip_address
        )
        
        # تحديث عداد المشاهدات في المقال
        article.views_count = ArticleView.objects.filter(article=article).count()
        article.save(update_fields=['views_count'])

def get_popular_articles(limit=5):
    """
    الحصول على المقالات الأكثر شعبية
    """
    from .models import Article
    from django.utils import timezone
    from datetime import timedelta
    
    # المقالات الأكثر مشاهدة في آخر 30 يوم
    thirty_days_ago = timezone.now() - timedelta(days=30)
    
    return Article.objects.filter(
        status='published',
        published_at__gte=thirty_days_ago
    ).order_by('-views_count', '-likes_count')[:limit]

def get_related_articles(article, limit=4):
    """
    الحصول على المقالات ذات الصلة
    """
    from .models import Article
    
    # البحث عن مقالات في نفس الفئة أو تحتوي على نفس العلامات
    related = Article.objects.filter(
        status='published'
    ).exclude(
        id=article.id
    )
    
    # أولوية للمقالات في نفس الفئة
    same_category = related.filter(category=article.category)[:limit//2]
    
    # مقالات تحتوي على نفس العلامات
    article_tags = article.tags.all()
    if article_tags.exists():
        same_tags = related.filter(
            tags__in=article_tags
        ).distinct()[:limit//2]
    else:
        same_tags = []
    
    # دمج النتائج
    related_articles = list(same_category) + list(same_tags)
    
    # إزالة المكررات والحد من العدد
    seen = set()
    unique_articles = []
    for art in related_articles:
        if art.id not in seen:
            unique_articles.append(art)
            seen.add(art.id)
        if len(unique_articles) >= limit:
            break
    
    # إذا لم نحصل على العدد المطلوب، نضيف مقالات عشوائية
    if len(unique_articles) < limit:
        remaining = limit - len(unique_articles)
        additional = related.exclude(
            id__in=[art.id for art in unique_articles]
        ).order_by('-published_at')[:remaining]
        unique_articles.extend(additional)
    
    return unique_articles

def search_articles(query, category=None, date_from=None, date_to=None):
    """
    البحث في المقالات
    """
    from .models import Article
    from django.db.models import Q
    
    articles = Article.objects.filter(status='published')
    
    if query:
        # البحث في العنوان والمحتوى والوصف
        search_query = Q(title__icontains=query) | \
                      Q(content__icontains=query) | \
                      Q(excerpt__icontains=query) | \
                      Q(tags__name__icontains=query)
        
        articles = articles.filter(search_query).distinct()
    
    if category:
        articles = articles.filter(category_id=category)
    
    if date_from:
        articles = articles.filter(published_at__date__gte=date_from)
    
    if date_to:
        articles = articles.filter(published_at__date__lte=date_to)
    
    return articles.order_by('-published_at')

def get_trending_tags(limit=10):
    """
    الحصول على العلامات الأكثر استخداماً
    """
    from .models import Tag
    from django.db.models import Count
    
    return Tag.objects.annotate(
        article_count=Count('articles', filter=Q(articles__status='published'))
    ).filter(
        article_count__gt=0
    ).order_by('-article_count')[:limit]

def format_reading_time(content):
    """
    حساب وقت القراءة المتوقع
    """
    import re
    from django.utils.html import strip_tags
    
    # إزالة HTML tags
    text = strip_tags(content)
    
    # عد الكلمات
    words = len(re.findall(r'\w+', text))
    
    # متوسط سرعة القراءة 200 كلمة في الدقيقة للعربية
    reading_time = max(1, round(words / 200))
    
    if reading_time == 1:
        return "دقيقة واحدة"
    elif reading_time <= 10:
        return f"{reading_time} دقائق"
    else:
        return f"{reading_time} دقيقة"

def generate_article_excerpt(content, max_length=200):
    """
    إنشاء مقتطف من المحتوى
    """
    from django.utils.html import strip_tags
    from django.utils.text import Truncator
    
    # إزالة HTML tags
    text = strip_tags(content)
    
    # اقتطاع النص
    truncator = Truncator(text)
    excerpt = truncator.chars(max_length, truncate='...')
    
    return excerpt