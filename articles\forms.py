from django import forms
from django.contrib.auth import get_user_model
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field, HTML
from crispy_forms.bootstrap import FormActions
from ckeditor_uploader.widgets import CKEditorUploadingWidget

from .models import Article, Category, Tag
from comments.models import Comment

User = get_user_model()

class ArticleForm(forms.ModelForm):
    """نموذج إنشاء وتحديث المقالات"""
    
    tags = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'placeholder': 'أدخل العلامات مفصولة بفاصلة',
            'class': 'form-control'
        }),
        help_text='أدخل العلامات مفصولة بفاصلة (مثال: تقنية، أخبار، رياضة)'
    )
    
    class Meta:
        model = Article
        fields = [
            'title', 'subtitle', 'content', 'excerpt', 'category', 
            'featured_image', 'image_alt', 'status', 'priority',
            'is_featured', 'allow_comments', 'is_breaking_news', 'tags'
        ]
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان المقال'
            }),
            'subtitle': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'العنوان الفرعي (اختياري)'
            }),
            'content': CKEditorUploadingWidget(attrs={
                'class': 'form-control'
            }),
            'excerpt': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'مقتطف من المقال (سيتم إنشاؤه تلقائياً إذا تُرك فارغاً)'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select'
            }),
            'featured_image': forms.FileInput(attrs={
                'class': 'form-control',
                'accept': 'image/*'
            }),
            'image_alt': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'النص البديل للصورة'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'is_featured': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'allow_comments': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'is_breaking_news': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'title': 'العنوان',
            'subtitle': 'العنوان الفرعي',
            'content': 'المحتوى',
            'excerpt': 'المقتطف',
            'category': 'الفئة',
            'featured_image': 'الصورة المميزة',
            'image_alt': 'النص البديل للصورة',
            'status': 'الحالة',
            'priority': 'الأولوية',
            'is_featured': 'مقال مميز',
            'allow_comments': 'السماح بالتعليقات',
            'is_breaking_news': 'خبر عاجل',
            'tags': 'العلامات',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تحديد الفئات النشطة فقط
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        
        # إعداد العلامات إذا كان المقال موجوداً
        if self.instance.pk:
            tags = self.instance.tags.all()
            self.fields['tags'].initial = ', '.join([tag.name for tag in tags])
        
        # إعداد Crispy Forms
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('title', css_class='col-md-8'),
                Column('status', css_class='col-md-4'),
            ),
            'subtitle',
            Row(
                Column('category', css_class='col-md-6'),
                Column('priority', css_class='col-md-6'),
            ),
            'content',
            'excerpt',
            Row(
                Column('featured_image', css_class='col-md-8'),
                Column('image_alt', css_class='col-md-4'),
            ),
            'tags',
            Row(
                Column(
                    Field('is_featured', wrapper_class='form-check'),
                    css_class='col-md-4'
                ),
                Column(
                    Field('allow_comments', wrapper_class='form-check'),
                    css_class='col-md-4'
                ),
                Column(
                    Field('is_breaking_news', wrapper_class='form-check'),
                    css_class='col-md-4'
                ),
            ),
            FormActions(
                Submit('submit', 'حفظ المقال', css_class='btn btn-primary btn-lg'),
                HTML('<a href="{% url "articles:home" %}" class="btn btn-secondary btn-lg ms-2">إلغاء</a>')
            )
        )
    
    def clean_tags(self):
        """تنظيف وإنشاء العلامات"""
        tags_str = self.cleaned_data.get('tags', '')
        if not tags_str:
            return []
        
        tag_names = [name.strip() for name in tags_str.split(',') if name.strip()]
        tags = []
        
        for name in tag_names:
            if len(name) > 50:  # حد أقصى لطول العلامة
                raise forms.ValidationError(f'العلامة "{name}" طويلة جداً (الحد الأقصى 50 حرف)')
            
            tag, created = Tag.objects.get_or_create(
                name=name,
                defaults={'slug': name}
            )
            tags.append(tag)
        
        return tags
    
    def save(self, commit=True):
        article = super().save(commit=False)
        
        if commit:
            article.save()
            
            # حفظ العلامات
            tags = self.cleaned_data.get('tags', [])
            if tags:
                article.tags.set(tags)
            
            self.save_m2m()
        
        return article

class CommentForm(forms.ModelForm):
    """نموذج التعليقات"""
    
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'اكتب تعليقك هنا...',
                'required': True
            })
        }
        labels = {
            'content': 'التعليق'
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'content',
            FormActions(
                Submit('submit', 'إرسال التعليق', css_class='btn btn-primary'),
            )
        )
    
    def clean_content(self):
        content = self.cleaned_data.get('content')
        
        if len(content) < 10:
            raise forms.ValidationError('التعليق قصير جداً (الحد الأدنى 10 أحرف)')
        
        if len(content) > 1000:
            raise forms.ValidationError('التعليق طويل جداً (الحد الأقصى 1000 حرف)')
        
        # فلترة الكلمات غير المناسبة (يمكن تطويرها أكثر)
        inappropriate_words = ['كلمة1', 'كلمة2']  # قائمة الكلمات المحظورة
        for word in inappropriate_words:
            if word in content.lower():
                raise forms.ValidationError('التعليق يحتوي على كلمات غير مناسبة')
        
        return content

class ArticleSearchForm(forms.Form):
    """نموذج البحث المتقدم"""
    
    SORT_CHOICES = [
        ('-created_at', 'الأحدث'),
        ('-views_count', 'الأكثر مشاهدة'),
        ('-likes_count', 'الأكثر إعجاباً'),
        ('title', 'الترتيب الأبجدي'),
    ]
    
    q = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'ابحث في العنوان والمحتوى...'
        }),
        label='كلمات البحث'
    )
    
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label='جميع الفئات',
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='الفئة'
    )
    
    tags = forms.ModelMultipleChoiceField(
        queryset=Tag.objects.all(),
        required=False,
        widget=forms.CheckboxSelectMultiple(),
        label='العلامات'
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='من تاريخ'
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='إلى تاريخ'
    )
    
    sort_by = forms.ChoiceField(
        choices=SORT_CHOICES,
        required=False,
        initial='-created_at',
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='ترتيب النتائج'
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'q',
            Row(
                Column('category', css_class='col-md-6'),
                Column('sort_by', css_class='col-md-6'),
            ),
            Row(
                Column('date_from', css_class='col-md-6'),
                Column('date_to', css_class='col-md-6'),
            ),
            'tags',
            FormActions(
                Submit('submit', 'بحث', css_class='btn btn-primary'),
                HTML('<a href="?" class="btn btn-secondary ms-2">إعادة تعيين</a>')
            )
        )

class CategoryForm(forms.ModelForm):
    """نموذج الفئات"""
    
    class Meta:
        model = Category
        fields = ['name', 'description', 'color', 'icon', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الفئة'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'وصف الفئة'
            }),
            'color': forms.TextInput(attrs={
                'class': 'form-control',
                'type': 'color'
            }),
            'icon': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم الأيقونة (Font Awesome)'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'name': 'اسم الفئة',
            'description': 'الوصف',
            'color': 'اللون',
            'icon': 'الأيقونة',
            'is_active': 'نشط',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'name',
            'description',
            Row(
                Column('color', css_class='col-md-6'),
                Column('icon', css_class='col-md-6'),
            ),
            Field('is_active', wrapper_class='form-check'),
            FormActions(
                Submit('submit', 'حفظ الفئة', css_class='btn btn-primary'),
            )
        )