# Generated by Django 5.1.5 on 2025-07-21 23:57

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Newsletter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('newsletter_type', models.CharField(choices=[('daily', 'يومية'), ('weekly', 'أسبوعية'), ('breaking', 'أخبار عاجلة'), ('custom', 'مخصصة')], default='custom', max_length=20, verbose_name='نوع النشرة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('scheduled', 'مجدولة'), ('sent', 'مُرسلة'), ('failed', 'فشلت')], default='draft', max_length=20, verbose_name='الحالة')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True, verbose_name='موعد الإرسال')),
                ('sent_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإرسال')),
                ('recipients_count', models.PositiveIntegerField(default=0, verbose_name='عدد المستلمين')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'نشرة بريدية',
                'verbose_name_plural': 'النشرات البريدية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='NewsletterTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القالب')),
                ('subject_template', models.CharField(max_length=200, verbose_name='قالب العنوان')),
                ('html_template', models.TextField(verbose_name='قالب HTML')),
                ('is_default', models.BooleanField(default=False, verbose_name='القالب الافتراضي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قالب نشرة',
                'verbose_name_plural': 'قوالب النشرات',
            },
        ),
        migrations.CreateModel(
            name='Subscriber',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='البريد الإلكتروني')),
                ('name', models.CharField(blank=True, max_length=100, verbose_name='الاسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('subscribed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاشتراك')),
                ('unsubscribed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ إلغاء الاشتراك')),
                ('daily_digest', models.BooleanField(default=True, verbose_name='الملخص اليومي')),
                ('weekly_digest', models.BooleanField(default=True, verbose_name='الملخص الأسبوعي')),
                ('breaking_news', models.BooleanField(default=True, verbose_name='الأخبار العاجلة')),
            ],
            options={
                'verbose_name': 'مشترك',
                'verbose_name_plural': 'المشتركون',
                'ordering': ['-subscribed_at'],
            },
        ),
    ]
