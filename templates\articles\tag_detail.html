{% extends 'base/base.html' %}
{% load static %}

{% block title %}العلامة: {{ tag.name }} - تاتا نيوز{% endblock %}

{% block meta_description %}مقالات تحتوي على علامة "{{ tag.name }}" في جريدة تاتا نيوز{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'articles:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item active">العلامة: {{ tag.name }}</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title">
                    <i class="fas fa-tag text-primary"></i>
                    العلامة: {{ tag.name }}
                </h2>
                {% if articles %}
                    <span class="badge bg-primary">{{ articles|length }} مقال</span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Articles -->
        <div class="col-lg-8">
            {% if articles %}
                <div class="row">
                    {% for article in articles %}
                    <div class="col-12 mb-4">
                        <div class="card article-card">
                            <div class="row g-0">
                                {% if article.featured_image %}
                                <div class="col-md-4">
                                    <div class="position-relative">
                                        <img src="{{ article.featured_image.url }}" class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="{{ article.image_alt|default:article.title }}">
                                        <span class="badge position-absolute top-0 end-0 m-2" style="background-color: {{ article.category.color }};">
                                            {{ article.category.name }}
                                        </span>
                                        {% if article.is_breaking_news %}
                                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">عاجل</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                                <div class="{% if article.featured_image %}col-md-8{% else %}col-12{% endif %}">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                                {{ article.title }}
                                            </a>
                                        </h5>
                                        <p class="card-text">{{ article.excerpt|truncatewords:30 }}</p>
                                        <div class="article-meta small text-muted">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                                    <span class="mx-2">|</span>
                                                    <i class="fas fa-calendar"></i> {{ article.published_at|date:"d M Y" }}
                                                </div>
                                                <div>
                                                    <i class="fas fa-eye"></i> {{ article.views_count }}
                                                    <span class="mx-2">|</span>
                                                    <i class="fas fa-heart"></i> {{ article.likes_count }}
                                                </div>
                                            </div>
                                        </div>
                                        {% if article.tags.exists %}
                                        <div class="mt-2">
                                            {% for article_tag in article.tags.all|slice:":4" %}
                                                <a href="{% url 'articles:tag_detail' article_tag.slug %}" class="tag {% if article_tag == tag %}active{% endif %}">#{{ article_tag.name }}</a>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="تنقل الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tag fa-3x text-muted mb-3"></i>
                    <h4>لا توجد مقالات بهذه العلامة</h4>
                    <p class="text-muted">لم يتم العثور على أي مقالات تحتوي على علامة "{{ tag.name }}".</p>
                    <a href="{% url 'articles:home' %}" class="btn btn-primary mt-3">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Related Tags -->
            <div class="sidebar">
                <h5>علامات ذات صلة</h5>
                <div class="d-flex flex-wrap">
                    {% for related_tag in related_tags %}
                        <a href="{% url 'articles:tag_detail' related_tag.slug %}" class="tag">{{ related_tag.name }}</a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Popular Tags -->
            <div class="sidebar">
                <h5>العلامات الشائعة</h5>
                <div class="d-flex flex-wrap">
                    {% for popular_tag in popular_tags %}
                        <a href="{% url 'articles:tag_detail' popular_tag.slug %}" class="tag">{{ popular_tag.name }}</a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Categories -->
            <div class="sidebar">
                <h5>تصفح حسب الفئة</h5>
                <div class="list-group list-group-flush">
                    {% for category in categories %}
                    <a href="{{ category.get_absolute_url }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span>
                            {% if category.icon %}
                                <i class="{{ category.icon }}" style="color: {{ category.color }};"></i>
                            {% endif %}
                            {{ category.name }}
                        </span>
                        <span class="badge rounded-pill" style="background-color: {{ category.color }};">{{ category.article_count|default:0 }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Newsletter -->
            <div class="newsletter-section">
                <h3>اشترك في نشرتنا البريدية</h3>
                <p>احصل على آخر المقالات المتعلقة بـ "{{ tag.name }}" مباشرة في بريدك الإلكتروني</p>
                <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                    {% csrf_token %}
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" name="email" placeholder="بريدك الإلكتروني" required>
                        <button class="btn btn-light" type="submit">اشتراك</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.tag.active {
    background-color: #007bff;
    color: white;
}
</style>
{% endblock %}