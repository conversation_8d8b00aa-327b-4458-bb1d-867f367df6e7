from django.db import models
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.utils.text import slugify
from ckeditor_uploader.fields import RichTextUploadingField
from PIL import Image
import os
import re

User = get_user_model()

def arabic_slugify(text):
    """إنشاء slug يدعم الأحرف العربية"""
    if not text:
        return ''
    
    # تنظيف النص
    text = text.strip()
    
    # استبدال المسافات بشرطات
    text = re.sub(r'\s+', '-', text)
    
    # إزالة الأحرف الخاصة غير المرغوب فيها
    text = re.sub(r'[^\w\s\u0600-\u06FF\-]', '', text)
    
    # استخدام slugify مع دعم Unicode
    slug = slugify(text, allow_unicode=True)
    
    return slug

class Category(models.Model):
    """نموذج الفئات"""
    name = models.CharField(max_length=100, unique=True, verbose_name='اسم الفئة')
    slug = models.SlugField(max_length=100, unique=True, blank=True, verbose_name='الرابط')
    description = models.TextField(blank=True, verbose_name='الوصف')
    color = models.CharField(max_length=7, default='#007bff', verbose_name='اللون')
    icon = models.CharField(max_length=50, blank=True, verbose_name='الأيقونة')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'فئة'
        verbose_name_plural = 'الفئات'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = arabic_slugify(self.name)
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return reverse('articles:category_detail', kwargs={'slug': self.slug})

class Tag(models.Model):
    """نموذج العلامات"""
    name = models.CharField(max_length=50, unique=True, verbose_name='اسم العلامة')
    slug = models.SlugField(max_length=50, unique=True, blank=True, verbose_name='الرابط')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'علامة'
        verbose_name_plural = 'العلامات'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = arabic_slugify(self.name)
        super().save(*args, **kwargs)

class Article(models.Model):
    """نموذج المقالات"""
    
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('published', 'منشور'),
        ('archived', 'مؤرشف'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'منخفضة'),
        ('normal', 'عادية'),
        ('high', 'عالية'),
        ('urgent', 'عاجل'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='العنوان')
    slug = models.SlugField(max_length=200, unique=True, blank=True, verbose_name='الرابط')
    subtitle = models.CharField(max_length=300, blank=True, verbose_name='العنوان الفرعي')
    content = RichTextUploadingField(verbose_name='المحتوى')
    excerpt = models.TextField(max_length=500, blank=True, verbose_name='المقتطف')
    
    # العلاقات
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='articles', verbose_name='الكاتب')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='articles', verbose_name='الفئة')
    tags = models.ManyToManyField(Tag, blank=True, related_name='articles', verbose_name='العلامات')
    
    # الصور
    featured_image = models.ImageField(upload_to='articles/', blank=True, null=True, verbose_name='الصورة المميزة')
    image_alt = models.CharField(max_length=200, blank=True, verbose_name='نص بديل للصورة')
    
    # الحالة والأولوية
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='الحالة')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='normal', verbose_name='الأولوية')
    
    # الإعدادات
    is_featured = models.BooleanField(default=False, verbose_name='مقال مميز')
    allow_comments = models.BooleanField(default=True, verbose_name='السماح بالتعليقات')
    is_breaking_news = models.BooleanField(default=False, verbose_name='خبر عاجل')
    
    # التواريخ
    published_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ النشر')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    # الإحصائيات
    views_count = models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')
    likes_count = models.PositiveIntegerField(default=0, verbose_name='عدد الإعجابات')
    
    class Meta:
        verbose_name = 'مقال'
        verbose_name_plural = 'المقالات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'published_at']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['author', 'status']),
        ]
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = arabic_slugify(self.title)
        
        # إنشاء مقتطف تلقائي إذا لم يكن موجوداً
        if not self.excerpt and self.content:
            from django.utils.html import strip_tags
            self.excerpt = strip_tags(self.content)[:300] + '...'
        
        super().save(*args, **kwargs)
        
        # تصغير حجم الصورة المميزة
        if self.featured_image:
            img = Image.open(self.featured_image.path)
            if img.height > 800 or img.width > 1200:
                output_size = (1200, 800)
                img.thumbnail(output_size)
                img.save(self.featured_image.path)
    
    def get_absolute_url(self):
        return reverse('articles:article_detail', kwargs={'slug': self.slug})
    
    @property
    def is_published(self):
        return self.status == 'published'
    
    def increment_views(self):
        """زيادة عدد المشاهدات"""
        self.views_count += 1
        self.save(update_fields=['views_count'])

class ArticleLike(models.Model):
    """نموذج إعجابات المقالات"""
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('article', 'user')
        verbose_name = 'إعجاب'
        verbose_name_plural = 'الإعجابات'

class ArticleView(models.Model):
    """نموذج مشاهدات المقالات"""
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='article_views')
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'مشاهدة'
        verbose_name_plural = 'المشاهدات'
