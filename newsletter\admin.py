from django.contrib import admin
from django.utils.html import format_html
from django.contrib import messages
from .models import Subscriber, Newsletter, NewsletterTemplate

@admin.register(Subscriber)
class SubscriberAdmin(admin.ModelAdmin):
    list_display = [
        'email', 'name', 'is_active', 'daily_digest',
        'weekly_digest', 'breaking_news', 'subscribed_at'
    ]
    list_filter = [
        'is_active', 'daily_digest', 'weekly_digest',
        'breaking_news', 'subscribed_at'
    ]
    search_fields = ['email', 'name']
    readonly_fields = ['subscribed_at', 'unsubscribed_at']
    
    fieldsets = (
        ('معلومات المشترك', {
            'fields': ('email', 'name', 'is_active')
        }),
        ('تفضيلات الاشتراك', {
            'fields': ('daily_digest', 'weekly_digest', 'breaking_news')
        }),
        ('التواريخ', {
            'fields': ('subscribed_at', 'unsubscribed_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['activate_subscribers', 'deactivate_subscribers', 'export_emails']
    
    def activate_subscribers(self, request, queryset):
        updated = queryset.update(is_active=True, unsubscribed_at=None)
        self.message_user(request, f'تم تفعيل {updated} مشترك.')
    activate_subscribers.short_description = 'تفعيل المشتركين المحددين'
    
    def deactivate_subscribers(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(is_active=False, unsubscribed_at=timezone.now())
        self.message_user(request, f'تم إلغاء تفعيل {updated} مشترك.')
    deactivate_subscribers.short_description = 'إلغاء تفعيل المشتركين'
    
    def export_emails(self, request, queryset):
        emails = list(queryset.values_list('email', flat=True))
        emails_str = ', '.join(emails)
        self.message_user(
            request,
            f'البريد الإلكتروني للمشتركين المحددين: {emails_str}',
            level=messages.INFO
        )
    export_emails.short_description = 'تصدير البريد الإلكتروني'

@admin.register(Newsletter)
class NewsletterAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'newsletter_type', 'status', 'recipients_count',
        'scheduled_at', 'sent_at', 'created_at'
    ]
    list_filter = ['newsletter_type', 'status', 'created_at', 'sent_at']
    search_fields = ['title', 'content']
    readonly_fields = ['recipients_count', 'sent_at', 'created_at', 'updated_at']
    
    fieldsets = (
        ('معلومات النشرة', {
            'fields': ('title', 'content', 'newsletter_type', 'status')
        }),
        ('الإرسال', {
            'fields': ('scheduled_at', 'sent_at', 'recipients_count')
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['send_newsletter', 'schedule_newsletter']
    
    def send_newsletter(self, request, queryset):
        sent_count = 0
        for newsletter in queryset.filter(status__in=['draft', 'scheduled']):
            try:
                count = newsletter.send_newsletter()
                sent_count += count
                self.message_user(
                    request,
                    f'تم إرسال النشرة "{newsletter.title}" إلى {count} مشترك.'
                )
            except Exception as e:
                self.message_user(
                    request,
                    f'خطأ في إرسال النشرة "{newsletter.title}": {str(e)}',
                    level=messages.ERROR
                )
        
        if sent_count > 0:
            self.message_user(
                request,
                f'تم إرسال النشرات إلى {sent_count} مشترك إجمالاً.'
            )
    send_newsletter.short_description = 'إرسال النشرات المحددة'
    
    def schedule_newsletter(self, request, queryset):
        updated = queryset.update(status='scheduled')
        self.message_user(request, f'تم جدولة {updated} نشرة.')
    schedule_newsletter.short_description = 'جدولة النشرات المحددة'

@admin.register(NewsletterTemplate)
class NewsletterTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_default', 'created_at']
    list_filter = ['is_default', 'created_at']
    search_fields = ['name', 'subject_template']
    
    def save_model(self, request, obj, form, change):
        # إذا تم تعيين هذا القالب كافتراضي، قم بإلغاء الافتراضي من الآخرين
        if obj.is_default:
            NewsletterTemplate.objects.filter(is_default=True).update(is_default=False)
        
        super().save_model(request, obj, form, change)
