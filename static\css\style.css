/* تاتا نيوز - ملف الأنماط الرئيسي */

/* الخطوط العربية */
body {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

/* إعدادات RTL */
.rtl {
    direction: rtl;
    text-align: right;
}

/* Header Styles */
.site-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.top-bar {
    font-size: 0.9rem;
}

.social-links a {
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #007bff !important;
}

.main-header .logo img {
    transition: transform 0.3s ease;
}

.main-header .logo img:hover {
    transform: scale(1.05);
}

.search-form .form-control {
    border-radius: 25px 0 0 25px;
    border: 2px solid #007bff;
    border-left: none;
}

.search-form .btn {
    border-radius: 0 25px 25px 0;
    border: 2px solid #007bff;
    border-right: none;
}

/* Navigation */
.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    right: 50%;
    background-color: white;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover::after {
    width: 100%;
    right: 0;
}

/* Breaking News Ticker */
.breaking-news {
    overflow: hidden;
    white-space: nowrap;
}

.ticker-content {
    display: inline-block;
    animation: ticker 30s linear infinite;
}

@keyframes ticker {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

/* Weather Widget */
.weather-widget {
    font-size: 0.9rem;
    background: rgba(255,255,255,0.1);
    padding: 5px 15px;
    border-radius: 20px;
}

/* Article Cards */
.article-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.article-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .card-img-top {
    transform: scale(1.05);
}

.article-card .card-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-card .card-title a:hover {
    color: #007bff;
}

/* Featured Article */
.featured-article {
    position: relative;
    height: 400px;
    overflow: hidden;
    border-radius: 15px;
}

.featured-article .card-img {
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
    transition: all 0.3s ease;
}

.featured-article:hover .card-img {
    transform: scale(1.05);
    filter: brightness(0.6);
}

.featured-article .card-img-overlay {
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.featured-article .card-title {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.featured-article .card-title a:hover {
    text-decoration: underline;
}

/* Category Badge */
.category-badge {
    font-size: 0.8rem;
    padding: 5px 12px;
    border-radius: 20px;
    font-weight: 500;
}

/* Article Meta */
.article-meta {
    font-size: 0.85rem;
    color: #666;
}

.article-meta i {
    margin-left: 5px;
    color: #007bff;
}

/* Tags */
.tag {
    display: inline-block;
    background: #e9ecef;
    color: #495057;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    text-decoration: none;
    margin-left: 5px;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.tag:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.tag.active {
    background: #007bff;
    color: white;
}

/* Sidebar */
.sidebar {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.sidebar h5 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-weight: 600;
}

/* Popular Articles */
.popular-article {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.popular-article:last-child {
    border-bottom: none;
}

.popular-article img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-left: 15px;
}

.popular-article h6 {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.popular-article h6 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.popular-article h6 a:hover {
    color: #007bff;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin-bottom: 30px;
}

.newsletter-section h3 {
    margin-bottom: 15px;
    font-weight: 600;
}

.newsletter-form .form-control {
    border: none;
    border-radius: 25px 0 0 25px;
    padding: 12px 20px;
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    padding: 12px 25px;
    font-weight: 500;
}

/* Article Detail Page */
.article-detail {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
}

.article-title {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1.3;
    color: #333;
    margin-bottom: 15px;
}

.article-subtitle {
    font-size: 1.3rem;
    font-weight: 400;
    line-height: 1.4;
}

.article-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #444;
}

.article-content h2,
.article-content h3,
.article-content h4 {
    color: #007bff;
    margin-top: 30px;
    margin-bottom: 15px;
}

.article-content p {
    margin-bottom: 20px;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 20px 0;
}

.article-content ul,
.article-content ol {
    margin-bottom: 20px;
    padding-right: 20px;
}

.article-content blockquote {
    background: #f8f9fa;
    border-right: 4px solid #007bff;
    padding: 20px;
    margin: 20px 0;
    border-radius: 5px;
    font-style: italic;
}

/* Featured Image */
.featured-image img {
    width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

/* Social Share Buttons */
.share-btn {
    margin-left: 10px;
    transition: all 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
}

/* Like Button */
.like-btn {
    transition: all 0.3s ease;
}

.like-btn.liked {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.like-btn:hover {
    transform: scale(1.05);
}

/* Author Bio */
.author-bio {
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.author-bio:hover {
    border-color: #007bff;
}

/* Comments Section */
.comments-section {
    margin-top: 40px;
}

.comment {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    transition: background-color 0.3s ease;
}

.comment:hover {
    background: #e9ecef;
}

.comment-reply {
    background: white;
    border: 1px solid #dee2e6;
}

.comment-author {
    color: #007bff;
    font-weight: 600;
}

.comment-date {
    font-size: 0.85rem;
}

.comment-actions .btn {
    font-size: 0.8rem;
    padding: 5px 10px;
    margin-left: 5px;
}

/* Reading Progress Bar */
.reading-progress {
    transition: width 0.3s ease;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    z-index: 1000;
    transition: all 0.3s ease;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.4);
}

/* Footer */
.site-footer {
    margin-top: 50px;
}

.footer-section h5,
.footer-section h6 {
    color: #007bff;
    margin-bottom: 20px;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white !important;
}

/* Pagination */
.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
    margin: 0 2px;
    border-radius: 5px;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #007bff;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 20px;
}

.breadcrumb-item a {
    color: #007bff;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Forms */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 10px;
    font-weight: 500;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .article-title {
        font-size: 1.8rem;
    }
    
    .featured-article {
        height: 300px;
    }
    
    .featured-article .card-title {
        font-size: 1.4rem;
    }
    
    .article-content {
        font-size: 1rem;
    }
    
    .popular-article img {
        width: 60px;
        height: 45px;
    }
    
    .newsletter-section {
        padding: 20px;
    }
    
    .back-to-top {
        bottom: 20px;
        left: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .main-header .row {
        text-align: center;
    }
    
    .main-header .col-lg-3,
    .main-header .col-lg-6,
    .main-header .col-lg-3 {
        margin-bottom: 15px;
    }
    
    .search-form {
        margin-bottom: 15px;
    }
    
    .article-card .card-img-top {
        height: 150px;
    }
    
    .featured-article {
        height: 250px;
    }
    
    .article-detail {
        padding: 20px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .article-card,
    .sidebar,
    .article-detail {
        background-color: #2d2d2d;
        color: #e0e0e0;
    }
    
    .article-card .card-title a {
        color: #e0e0e0;
    }
    
    .comment {
        background-color: #3d3d3d;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .sidebar,
    .social-share,
    .comments-section,
    .back-to-top {
        display: none !important;
    }
    
    .article-detail {
        box-shadow: none;
        padding: 0;
    }
    
    .article-content {
        font-size: 12pt;
        line-height: 1.5;
    }
}