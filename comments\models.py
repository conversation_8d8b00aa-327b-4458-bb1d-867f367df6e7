from django.db import models
from django.contrib.auth import get_user_model
from articles.models import Article

User = get_user_model()

class Comment(models.Model):
    """نموذج التعليقات"""
    
    STATUS_CHOICES = [
        ('pending', 'في الانتظار'),
        ('approved', 'موافق عليه'),
        ('rejected', 'مرفوض'),
    ]
    
    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='comments', verbose_name='المقال')
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments', verbose_name='الكاتب')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='replies', verbose_name='التعليق الأصلي')
    
    content = models.TextField(verbose_name='المحتوى')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='الحالة')
    
    # الإحصائيات
    likes_count = models.PositiveIntegerField(default=0, verbose_name='عدد الإعجابات')
    
    # التواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    class Meta:
        verbose_name = 'تعليق'
        verbose_name_plural = 'التعليقات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['article', 'status']),
            models.Index(fields=['author', 'status']),
        ]
    
    def __str__(self):
        return f'تعليق {self.author.get_full_name()} على {self.article.title}'
    
    @property
    def is_approved(self):
        return self.status == 'approved'
    
    @property
    def is_reply(self):
        return self.parent is not None
    
    def get_replies(self):
        return self.replies.filter(status='approved').order_by('created_at')

class CommentLike(models.Model):
    """نموذج إعجابات التعليقات"""
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('comment', 'user')
        verbose_name = 'إعجاب تعليق'
        verbose_name_plural = 'إعجابات التعليقات'
