# إعدادات تاتا نيوز البيئية
# انسخ هذا الملف إلى .env وقم بتعديل القيم

# إعدادات Django الأساسية
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# إعدادات قاعدة البيانات
DB_NAME=tatanews_db
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# إعدادات البريد الإلكتروني
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# إعدادات Redis (اختياري)
REDIS_URL=redis://127.0.0.1:6379/1

# إعدادات Sentry (اختياري)
SENTRY_DSN=https://your-sentry-dsn

# إعدادات AWS S3 (اختياري)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-bucket-name
AWS_S3_REGION_NAME=us-east-1

# إعدادات وسائل التواصل الاجتماعي (اختياري)
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# إعدادات أخرى
SITE_URL=https://yourdomain.com
ADMIN_EMAIL=<EMAIL>