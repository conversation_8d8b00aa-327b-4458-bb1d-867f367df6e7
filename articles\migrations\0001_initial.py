# Generated by Django 5.1.5 on 2025-07-21 23:57

import ckeditor_uploader.fields
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الفئة')),
                ('slug', models.SlugField(blank=True, max_length=100, unique=True, verbose_name='الرابط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('color', models.CharField(default='#007bff', max_length=7, verbose_name='اللون')),
                ('icon', models.CharField(blank=True, max_length=50, verbose_name='الأيقونة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'فئة',
                'verbose_name_plural': 'الفئات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='اسم العلامة')),
                ('slug', models.SlugField(blank=True, unique=True, verbose_name='الرابط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'علامة',
                'verbose_name_plural': 'العلامات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Article',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True, verbose_name='الرابط')),
                ('subtitle', models.CharField(blank=True, max_length=300, verbose_name='العنوان الفرعي')),
                ('content', ckeditor_uploader.fields.RichTextUploadingField(verbose_name='المحتوى')),
                ('excerpt', models.TextField(blank=True, max_length=500, verbose_name='المقتطف')),
                ('featured_image', models.ImageField(blank=True, null=True, upload_to='articles/', verbose_name='الصورة المميزة')),
                ('image_alt', models.CharField(blank=True, max_length=200, verbose_name='نص بديل للصورة')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('published', 'منشور'), ('archived', 'مؤرشف')], default='draft', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('normal', 'عادية'), ('high', 'عالية'), ('urgent', 'عاجل')], default='normal', max_length=20, verbose_name='الأولوية')),
                ('is_featured', models.BooleanField(default=False, verbose_name='مقال مميز')),
                ('allow_comments', models.BooleanField(default=True, verbose_name='السماح بالتعليقات')),
                ('is_breaking_news', models.BooleanField(default=False, verbose_name='خبر عاجل')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ النشر')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('views_count', models.PositiveIntegerField(default=0, verbose_name='عدد المشاهدات')),
                ('likes_count', models.PositiveIntegerField(default=0, verbose_name='عدد الإعجابات')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to=settings.AUTH_USER_MODEL, verbose_name='الكاتب')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='articles', to='articles.category', verbose_name='الفئة')),
                ('tags', models.ManyToManyField(blank=True, related_name='articles', to='articles.tag', verbose_name='العلامات')),
            ],
            options={
                'verbose_name': 'مقال',
                'verbose_name_plural': 'المقالات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ArticleView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='article_views', to='articles.article')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'مشاهدة',
                'verbose_name_plural': 'المشاهدات',
            },
        ),
        migrations.CreateModel(
            name='ArticleLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='articles.article')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'إعجاب',
                'verbose_name_plural': 'الإعجابات',
                'unique_together': {('article', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['status', 'published_at'], name='articles_ar_status_7759bd_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['category', 'status'], name='articles_ar_categor_961ad7_idx'),
        ),
        migrations.AddIndex(
            model_name='article',
            index=models.Index(fields=['author', 'status'], name='articles_ar_author__8254d9_idx'),
        ),
    ]
