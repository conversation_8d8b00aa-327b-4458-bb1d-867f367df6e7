from django.urls import path, re_path
from . import views

app_name = 'articles'

urlpatterns = [
    # الصفحة الرئيسية
    path('', views.HomeView.as_view(), name='home'),
    
    # المقالات
    path('latest/', views.LatestArticlesView.as_view(), name='latest'),
    path('featured/', views.FeaturedArticlesView.as_view(), name='featured'),
    path('create/', views.ArticleCreateView.as_view(), name='create'),
    re_path(r'^article/(?P<slug>[\w\-\u0600-\u06FF]+)/$', views.ArticleDetailView.as_view(), name='article_detail'),
    re_path(r'^article/(?P<slug>[\w\-\u0600-\u06FF]+)/edit/$', views.ArticleUpdateView.as_view(), name='article_edit'),
    re_path(r'^article/(?P<slug>[\w\-\u0600-\u06FF]+)/delete/$', views.ArticleDeleteView.as_view(), name='article_delete'),
    
    # الإعجابات والتعليقات
    path('article/<int:article_id>/like/', views.toggle_like, name='toggle_like'),
    path('article/<int:article_id>/comment/', views.add_comment, name='add_comment'),
    
    # الفئات والعلامات
    re_path(r'^category/(?P<slug>[\w\-\u0600-\u06FF]+)/$', views.category_detail, name='category_detail'),
    re_path(r'^tag/(?P<slug>[\w\-\u0600-\u06FF]+)/$', views.tag_detail, name='tag_detail'),
    path('author/<int:author_id>/', views.author_articles, name='author_articles'),
    
    # البحث
    path('search/', views.SearchView.as_view(), name='search'),
]