from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import DetailView, UpdateView, ListView
from django.contrib import messages
from django.urls import reverse_lazy
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import CustomUser
from .forms import ProfileForm
from articles.models import Article
from comments.models import Comment

class ProfileView(LoginRequiredMixin, DetailView):
    """عرض الملف الشخصي"""
    model = CustomUser
    template_name = 'accounts/profile.html'
    context_object_name = 'profile_user'
    
    def get_object(self):
        return self.request.user
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.object
        
        # إحصائيات المستخدم
        context['articles_count'] = user.articles.filter(status='published').count()
        context['comments_count'] = user.comments.filter(status='approved').count()
        context['total_views'] = user.articles.aggregate(
            total=Count('views_count')
        )['total'] or 0
        context['total_likes'] = user.articles.aggregate(
            total=Count('likes_count')
        )['total'] or 0
        
        # آخر المقالات
        context['recent_articles'] = user.articles.filter(
            status='published'
        ).order_by('-published_at')[:5]
        
        # آخر التعليقات
        context['recent_comments'] = user.comments.filter(
            status='approved'
        ).select_related('article').order_by('-created_at')[:5]
        
        return context

class ProfileEditView(LoginRequiredMixin, UpdateView):
    """تحديث الملف الشخصي"""
    model = CustomUser
    form_class = ProfileForm
    template_name = 'accounts/profile_edit.html'
    success_url = reverse_lazy('accounts:profile')
    
    def get_object(self):
        return self.request.user
    
    def form_valid(self, form):
        messages.success(self.request, 'تم تحديث الملف الشخصي بنجاح!')
        return super().form_valid(form)

class MyArticlesView(LoginRequiredMixin, ListView):
    """مقالات المستخدم"""
    model = Article
    template_name = 'accounts/my_articles.html'
    context_object_name = 'articles'
    paginate_by = 12
    
    def get_queryset(self):
        return Article.objects.filter(
            author=self.request.user
        ).select_related('category').prefetch_related('tags')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # إحصائيات المقالات
        context['published_count'] = user.articles.filter(status='published').count()
        context['draft_count'] = user.articles.filter(status='draft').count()
        context['archived_count'] = user.articles.filter(status='archived').count()
        
        return context

class DashboardView(LoginRequiredMixin, ListView):
    """لوحة التحكم"""
    template_name = 'accounts/dashboard.html'
    context_object_name = 'articles'
    paginate_by = 10
    
    def get_queryset(self):
        if self.request.user.can_moderate:
            return Article.objects.all().select_related('author', 'category')
        else:
            return Article.objects.filter(
                author=self.request.user
            ).select_related('category')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # إحصائيات عامة
        if user.can_moderate:
            # إحصائيات للمحررين والمدراء
            context['total_articles'] = Article.objects.count()
            context['published_articles'] = Article.objects.filter(status='published').count()
            context['pending_articles'] = Article.objects.filter(status='draft').count()
            context['total_comments'] = Comment.objects.count()
            context['pending_comments'] = Comment.objects.filter(status='pending').count()
            context['total_users'] = CustomUser.objects.count()
            context['new_users_this_week'] = CustomUser.objects.filter(
                created_at__gte=timezone.now() - timedelta(days=7)
            ).count()
            
            # المقالات الأكثر مشاهدة هذا الأسبوع
            context['trending_articles'] = Article.objects.filter(
                status='published',
                created_at__gte=timezone.now() - timedelta(days=7)
            ).order_by('-views_count')[:5]
            
            # التعليقات المعلقة
            context['pending_comments_list'] = Comment.objects.filter(
                status='pending'
            ).select_related('author', 'article')[:5]
            
        else:
            # إحصائيات للكتاب
            context['my_articles_count'] = user.articles.count()
            context['my_published_count'] = user.articles.filter(status='published').count()
            context['my_draft_count'] = user.articles.filter(status='draft').count()
            context['my_total_views'] = user.articles.aggregate(
                total=Count('views_count')
            )['total'] or 0
            context['my_total_likes'] = user.articles.aggregate(
                total=Count('likes_count')
            )['total'] or 0
            
            # أداء المقالات
            context['top_articles'] = user.articles.filter(
                status='published'
            ).order_by('-views_count')[:5]
        
        return context
