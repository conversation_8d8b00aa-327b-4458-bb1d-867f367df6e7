/* تاتا نيوز - الأنماط الرئيسية */

/* الخطوط */
body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
}

h1, h2, h3, h4, h5, h6 {
    font-family: '<PERSON><PERSON>', serif;
    font-weight: 700;
}

/* الألوان الأساسية */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #f8f9fa;
    --accent-color: #dc3545;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --border-color: #dee2e6;
}

/* Header Styles */
.header-main {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.top-bar {
    font-size: 0.9rem;
}

.social-links a {
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: var(--accent-color) !important;
}

.logo h1 {
    font-size: 2.5rem;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-form .form-control {
    border-radius: 25px 0 0 25px;
    border: 2px solid var(--primary-color);
}

.search-form .btn {
    border-radius: 0 25px 25px 0;
    border: 2px solid var(--primary-color);
}

/* Navigation */
.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
    border-radius: 5px;
}

/* Breaking News */
.breaking-news {
    background: var(--accent-color);
    padding: 5px 15px;
    border-radius: 20px;
    max-width: 300px;
    overflow: hidden;
}

.breaking-news marquee {
    font-size: 0.9rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

/* Article Cards */
.article-card {
    margin-bottom: 2rem;
}

.article-meta {
    font-size: 0.9rem;
    color: #666;
}

.article-meta i {
    margin-left: 5px;
}

.category-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

/* Featured Article */
.featured-article {
    position: relative;
    height: 400px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    overflow: hidden;
    color: white;
}

.featured-article .card-img-overlay {
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
}

.featured-article h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Sidebar */
.sidebar {
    background: var(--secondary-color);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.sidebar h5 {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

/* Popular Articles */
.popular-article {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.popular-article:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.popular-article img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-left: 1rem;
}

.popular-article h6 {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.popular-article small {
    color: #666;
}

/* Comments */
.comment {
    background: var(--light-gray);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.comment-author {
    font-weight: 600;
    color: var(--primary-color);
}

.comment-date {
    font-size: 0.8rem;
    color: #666;
}

.comment-reply {
    margin-right: 3rem;
    border-right: 3px solid var(--primary-color);
    padding-right: 1rem;
}

/* Forms */
.form-control {
    border-radius: 10px;
    border: 2px solid var(--border-color);
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
}

.btn {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #3d6bb3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #1e4080, var(--primary-color));
    transform: translateY(-2px);
}

/* Pagination */
.pagination .page-link {
    border-radius: 10px;
    margin: 0 2px;
    border: 2px solid var(--border-color);
    color: var(--primary-color);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
}

.newsletter-section h3 {
    margin-bottom: 1rem;
}

.newsletter-section .form-control {
    border-radius: 25px;
    border: none;
    padding: 1rem;
}

.newsletter-section .btn {
    border-radius: 25px;
    padding: 1rem 2rem;
    background: white;
    color: var(--primary-color);
    border: none;
}

/* Footer */
.footer {
    margin-top: auto;
}

.footer h5, .footer h6 {
    color: white;
}

.footer .social-links a:hover {
    color: var(--accent-color) !important;
    transform: translateY(-2px);
}

/* Back to Top */
#backToTop {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    transition: all 0.3s ease;
}

#backToTop:hover {
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .logo h1 {
        font-size: 1.8rem;
    }
    
    .featured-article {
        height: 300px;
    }
    
    .featured-article h2 {
        font-size: 1.5rem;
    }
    
    .popular-article {
        flex-direction: column;
    }
    
    .popular-article img {
        width: 100%;
        height: 150px;
        margin-left: 0;
        margin-bottom: 1rem;
    }
    
    .comment-reply {
        margin-right: 1rem;
    }
    
    .newsletter-section {
        padding: 2rem 1rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Article Content Styles */
.article-content {
    font-size: 1.1rem;
    line-height: 1.8;
}

.article-content h2, .article-content h3 {
    color: var(--primary-color);
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    margin: 1.5rem 0;
}

.article-content blockquote {
    border-right: 4px solid var(--primary-color);
    padding: 1rem 2rem;
    background: var(--light-gray);
    border-radius: 0 10px 10px 0;
    font-style: italic;
    margin: 2rem 0;
}

/* Tags */
.tag {
    display: inline-block;
    background: var(--light-gray);
    color: var(--text-color);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin: 0.25rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
}

/* Search Results */
.search-results .highlight {
    background-color: yellow;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #e9ecef;
        --light-gray: #343a40;
        --border-color: #495057;
    }
    
    body {
        background-color: #212529;
        color: var(--text-color);
    }
    
    .card {
        background-color: #343a40;
        color: var(--text-color);
    }
    
    .sidebar {
        background-color: #343a40;
    }
}

/* Print Styles */
@media print {
    .header-main,
    .footer,
    .sidebar,
    #backToTop {
        display: none !important;
    }
    
    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .article-content {
        font-size: 12pt;
        line-height: 1.5;
    }
}