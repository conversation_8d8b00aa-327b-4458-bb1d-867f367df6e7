/* تاتا نيوز - التصميم العصري */

/* CSS Custom Properties for Modern Design */
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Neutral Colors */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* Accent Colors */
  --accent-red: #ef4444;
  --accent-green: #10b981;
  --accent-yellow: #f59e0b;
  --accent-purple: #8b5cf6;

  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Typography */
  --font-family-primary: 'Noto Sans Arabic', 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-heading: 'Noto Sans Arabic', 'Inter', system-ui, -apple-system, sans-serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-purple) 0%, var(--primary-600) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Dark Theme Variables */
[data-bs-theme="dark"] {
  --neutral-50: #111827;
  --neutral-100: #1f2937;
  --neutral-200: #374151;
  --neutral-300: #4b5563;
  --neutral-400: #6b7280;
  --neutral-500: #9ca3af;
  --neutral-600: #d1d5db;
  --neutral-700: #e5e7eb;
  --neutral-800: #f3f4f6;
  --neutral-900: #f9fafb;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--neutral-800);
  background-color: var(--neutral-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: 1.2;
  color: var(--neutral-900);
  margin-bottom: var(--space-md);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin-bottom: var(--space-md);
  color: var(--neutral-700);
}

a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-700);
}

/* Modern Card Design */
.modern-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
  transition: all var(--transition-normal);
  overflow: hidden;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

[data-bs-theme="dark"] .modern-card {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
}

/* Glass Morphism Effect */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
}

[data-bs-theme="dark"] .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.1);
}

/* Modern Buttons */
.btn-modern {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
}

.btn-modern-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-modern-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-modern-secondary {
  background: var(--neutral-100);
  color: var(--neutral-700);
  border: 1px solid var(--neutral-300);
}

.btn-modern-secondary:hover {
  background: var(--neutral-200);
  color: var(--neutral-800);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: var(--radius-sm);
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Modern Navigation */
.modern-nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--neutral-200);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
}

[data-bs-theme="dark"] .modern-nav {
  background: rgba(17, 24, 39, 0.95);
  border-bottom-color: var(--neutral-200);
}

/* Micro Interactions */
.hover-lift {
  transition: transform var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--transition-fast);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Focus States */
.focus-ring:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Responsive Design Utilities */
@media (max-width: 768px) {
  :root {
    --text-4xl: 2rem;
    --text-3xl: 1.5rem;
    --text-2xl: 1.25rem;
  }
  
  .modern-card {
    border-radius: var(--radius-md);
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(30px); }
  to { opacity: 1; transform: translateX(0); }
}

/* Modern Form Elements */
.modern-input {
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: white;
}

.modern-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

[data-bs-theme="dark"] .modern-input {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
  color: var(--neutral-800);
}

/* Advanced Grid Layouts */
.modern-grid {
  display: grid;
  gap: var(--space-lg);
}

.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

.grid-2-cols {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3-cols {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4-cols {
  grid-template-columns: repeat(4, 1fr);
}

/* Modern Flexbox Utilities */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-evenly {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

/* Advanced Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Animation Classes */
.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideInDown 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Modern Scroll Behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

.scroll-snap-x {
  scroll-snap-type: x mandatory;
  overflow-x: auto;
}

.scroll-snap-y {
  scroll-snap-type: y mandatory;
  overflow-y: auto;
}

.scroll-snap-start {
  scroll-snap-align: start;
}

.scroll-snap-center {
  scroll-snap-align: center;
}

/* Advanced Shadow System */
.shadow-colored {
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
}

.shadow-inner {
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Modern Border Styles */
.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              var(--gradient-primary) border-box;
}

[data-bs-theme="dark"] .border-gradient {
  background: linear-gradient(var(--neutral-100), var(--neutral-100)) padding-box,
              var(--gradient-primary) border-box;
}

/* Advanced Typography */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-outline {
  -webkit-text-stroke: 1px var(--neutral-300);
}

/* Modern Image Effects */
.image-overlay {
  position: relative;
  overflow: hidden;
}

.image-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.8), rgba(139, 92, 246, 0.8));
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.image-overlay:hover::before {
  opacity: 1;
}

.image-zoom {
  overflow: hidden;
}

.image-zoom img {
  transition: transform var(--transition-slow);
}

.image-zoom:hover img {
  transform: scale(1.1);
}

/* Modern Progress Bars */
.progress-modern {
  height: 8px;
  background: var(--neutral-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.progress-modern .progress-bar {
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  transition: width var(--transition-normal);
}

/* Modern Badges */
.badge-modern {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-lg);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-gradient {
  background: var(--gradient-primary);
  color: white;
}

.badge-outline {
  background: transparent;
  border: 1px solid var(--primary-300);
  color: var(--primary-700);
}

/* Modern Tooltips */
.tooltip-modern {
  position: relative;
  cursor: help;
}

.tooltip-modern::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--neutral-900);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: 1000;
}

.tooltip-modern::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--neutral-900);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
}

.tooltip-modern:hover::before,
.tooltip-modern:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-4px);
}

/* Modern Navigation Styles */
.nav-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all var(--transition-normal);
}

[data-bs-theme="dark"] .nav-modern {
  background: rgba(17, 24, 39, 0.95);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.nav-modern.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

[data-bs-theme="dark"] .nav-modern.scrolled {
  background: rgba(17, 24, 39, 0.98);
}

/* Modern Article Cards */
.article-card-modern {
  background: white;
  border-radius: var(--radius-xl);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--neutral-200);
}

.article-card-modern:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

[data-bs-theme="dark"] .article-card-modern {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
}

.article-card-modern .card-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.article-card-modern .card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.article-card-modern:hover .card-image img {
  transform: scale(1.1);
}

/* Modern Sidebar Widgets */
.sidebar-widget-modern {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--neutral-200);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.sidebar-widget-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

[data-bs-theme="dark"] .sidebar-widget-modern {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
}

.widget-header-modern {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--neutral-50);
}

[data-bs-theme="dark"] .widget-header-modern {
  background: var(--neutral-200);
  border-bottom-color: var(--neutral-300);
}

/* Modern Form Components */
.form-group-modern {
  position: relative;
  margin-bottom: var(--space-lg);
}

.form-label-modern {
  position: absolute;
  top: 50%;
  right: var(--space-md);
  transform: translateY(-50%);
  color: var(--neutral-500);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  pointer-events: none;
  z-index: 1;
}

.form-input-modern {
  width: 100%;
  padding: var(--space-md);
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  background: white;
  transition: all var(--transition-fast);
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input-modern:focus + .form-label-modern,
.form-input-modern:not(:placeholder-shown) + .form-label-modern {
  top: 0;
  right: var(--space-sm);
  font-size: var(--text-sm);
  color: var(--primary-600);
  background: white;
  padding: 0 var(--space-xs);
}

[data-bs-theme="dark"] .form-input-modern {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
  color: var(--neutral-800);
}

[data-bs-theme="dark"] .form-input-modern:focus + .form-label-modern,
[data-bs-theme="dark"] .form-input-modern:not(:placeholder-shown) + .form-label-modern {
  background: var(--neutral-100);
}

/* Modern Pagination */
.pagination-modern {
  display: flex;
  gap: var(--space-sm);
  justify-content: center;
  align-items: center;
}

.pagination-modern .page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--neutral-300);
  background: white;
  color: var(--neutral-700);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.pagination-modern .page-link:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-2px);
}

.pagination-modern .page-link.active {
  background: var(--gradient-primary);
  border-color: var(--primary-600);
  color: white;
}

/* Modern Search Component */
.search-modern {
  position: relative;
  max-width: 400px;
}

.search-modern .search-input {
  width: 100%;
  padding: var(--space-sm) var(--space-md);
  padding-left: 3rem;
  border: 2px solid var(--neutral-300);
  border-radius: var(--radius-xl);
  font-size: var(--text-base);
  background: white;
  transition: all var(--transition-fast);
}

.search-modern .search-input:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-modern .search-icon {
  position: absolute;
  left: var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--neutral-500);
  pointer-events: none;
}

/* Modern Tags */
.tag-modern {
  display: inline-flex;
  align-items: center;
  padding: var(--space-xs) var(--space-sm);
  background: var(--primary-50);
  color: var(--primary-700);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.tag-modern:hover {
  background: var(--primary-600);
  color: white;
  transform: translateY(-1px);
}

/* Modern Stats Cards */
.stats-card-modern {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  border: 1px solid var(--neutral-200);
  text-align: center;
  transition: all var(--transition-normal);
}

.stats-card-modern:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stats-card-modern .stats-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--space-md);
  background: var(--gradient-primary);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.stats-card-modern .stats-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--neutral-900);
  margin-bottom: var(--space-xs);
}

.stats-card-modern .stats-label {
  color: var(--neutral-600);
  font-size: var(--text-sm);
}

/* Responsive Design Utilities */
@media (max-width: 1200px) {
  .grid-4-cols {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 992px) {
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  :root {
    --space-xs: 0.2rem;
    --space-sm: 0.4rem;
    --space-md: 0.8rem;
    --space-lg: 1.2rem;
    --space-xl: 1.6rem;
    --space-2xl: 2.4rem;
    --space-3xl: 3.2rem;
  }

  .grid-2-cols,
  .grid-3-cols,
  .grid-4-cols {
    grid-template-columns: 1fr;
  }

  .grid-auto-fit,
  .grid-auto-fill {
    grid-template-columns: 1fr;
  }

  .modern-card {
    border-radius: var(--radius-md);
  }

  .article-card-modern .card-image {
    height: 180px;
  }
}

@media (max-width: 576px) {
  .flex-between,
  .flex-around,
  .flex-evenly {
    flex-direction: column;
    gap: var(--space-md);
  }

  .pagination-modern {
    flex-wrap: wrap;
  }

  .search-modern {
    max-width: 100%;
  }
}

/* Modern Interaction Animations */
@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes rubber {
  0% {
    transform: scale3d(1, 1, 1);
  }
  30% {
    transform: scale3d(1.25, 0.75, 1);
  }
  40% {
    transform: scale3d(0.75, 1.25, 1);
  }
  50% {
    transform: scale3d(1.15, 0.85, 1);
  }
  65% {
    transform: scale3d(0.95, 1.05, 1);
  }
  75% {
    transform: scale3d(1.05, 0.95, 1);
  }
  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes swing {
  20% {
    transform: rotate3d(0, 0, 1, 15deg);
  }
  40% {
    transform: rotate3d(0, 0, 1, -10deg);
  }
  60% {
    transform: rotate3d(0, 0, 1, 5deg);
  }
  80% {
    transform: rotate3d(0, 0, 1, -5deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
}

@keyframes wobble {
  0% {
    transform: translate3d(0, 0, 0);
  }
  15% {
    transform: translate3d(-25%, 0, 0) rotate3d(0, 0, 1, -5deg);
  }
  30% {
    transform: translate3d(20%, 0, 0) rotate3d(0, 0, 1, 3deg);
  }
  45% {
    transform: translate3d(-15%, 0, 0) rotate3d(0, 0, 1, -3deg);
  }
  60% {
    transform: translate3d(10%, 0, 0) rotate3d(0, 0, 1, 2deg);
  }
  75% {
    transform: translate3d(-5%, 0, 0) rotate3d(0, 0, 1, -1deg);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}

/* Animation Classes */
.animate-bounce {
  animation: bounce 1s ease-in-out;
}

.animate-rubber {
  animation: rubber 1s ease-in-out;
}

.animate-swing {
  animation: swing 1s ease-in-out;
}

.animate-wobble {
  animation: wobble 1s ease-in-out;
}

/* Modern Notification Styles */
.modern-notification {
  font-family: var(--font-family-primary);
  font-size: var(--text-sm);
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.modern-notification .notification-content {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.modern-notification i {
  font-size: 1.125rem;
}

/* Interactive Elements */
.interactive-element {
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
}

.interactive-element:hover {
  transform: translateY(-2px);
}

.interactive-element:active {
  transform: translateY(0) scale(0.98);
}

/* Modern Tooltip Styles */
.modern-tooltip {
  font-family: var(--font-family-primary);
  font-size: var(--text-xs);
  font-weight: 500;
  letter-spacing: 0.025em;
}

.modern-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: var(--neutral-900);
}

/* Touch-friendly Elements */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover {
    transform: none;
  }

  .hover-scale:hover {
    transform: none;
  }

  .interactive-element {
    min-height: 44px;
    min-width: 44px;
  }

  .btn-modern {
    min-height: 44px;
    padding: var(--space-sm) var(--space-lg);
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus Styles */
.focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .modern-card {
    border: 2px solid var(--neutral-900);
  }

  .btn-modern-primary {
    border: 2px solid var(--primary-900);
  }

  .btn-modern-secondary {
    border: 2px solid var(--neutral-900);
  }
}

/* Print Optimizations */
@media print {
  .modern-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .btn-modern,
  .interactive-element {
    display: none;
  }

  .modern-tooltip {
    display: none;
  }

  .modern-notification {
    display: none;
  }
}
