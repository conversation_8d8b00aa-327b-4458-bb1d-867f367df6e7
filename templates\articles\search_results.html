{% extends 'base/base.html' %}
{% load static %}

{% block title %}نتائج البحث{% if query %} عن "{{ query }}"{% endif %} - تاتا نيوز{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'articles:home' %}">الرئيسية</a></li>
                    <li class="breadcrumb-item active">نتائج البحث</li>
                </ol>
            </nav>
            
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title">
                    <i class="fas fa-search text-primary"></i>
                    نتائج البحث
                    {% if query %}
                        <small class="text-muted">عن "{{ query }}"</small>
                    {% endif %}
                </h2>
                {% if articles %}
                    <span class="badge bg-primary">{{ articles|length }} نتيجة</span>
                {% endif %}
            </div>
            
            <!-- Advanced Search Form -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <button class="btn btn-link text-decoration-none" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearch">
                            <i class="fas fa-filter"></i> البحث المتقدم
                        </button>
                    </h6>
                </div>
                <div class="collapse" id="advancedSearch">
                    <div class="card-body">
                        <form method="GET" action="{% url 'articles:search' %}">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="q" class="form-label">كلمات البحث</label>
                                    <input type="text" class="form-control" id="q" name="q" value="{{ query }}" placeholder="ابحث في العنوان والمحتوى...">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="category" class="form-label">الفئة</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">جميع الفئات</option>
                                        {% for category in categories %}
                                            <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>
                                                {{ category.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="date_from" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ request.GET.date_from }}">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="date_to" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ request.GET.date_to }}">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <a href="{% url 'articles:search' %}" class="btn btn-secondary ms-2">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Search Results -->
        <div class="col-lg-8">
            {% if articles %}
                <div class="row">
                    {% for article in articles %}
                    <div class="col-12 mb-4">
                        <div class="card article-card">
                            <div class="row g-0">
                                {% if article.featured_image %}
                                <div class="col-md-4">
                                    <div class="position-relative">
                                        <img src="{{ article.featured_image.url }}" class="img-fluid rounded-start h-100" style="object-fit: cover;" alt="{{ article.image_alt|default:article.title }}">
                                        <span class="badge position-absolute top-0 end-0 m-2" style="background-color: {{ article.category.color }};">
                                            {{ article.category.name }}
                                        </span>
                                        {% if article.is_breaking_news %}
                                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">عاجل</span>
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                                <div class="{% if article.featured_image %}col-md-8{% else %}col-12{% endif %}">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                                {{ article.title }}
                                            </a>
                                        </h5>
                                        <p class="card-text">{{ article.excerpt|truncatewords:30 }}</p>
                                        <div class="article-meta small text-muted">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                                    <span class="mx-2">|</span>
                                                    <i class="fas fa-calendar"></i> {{ article.published_at|date:"d M Y" }}
                                                </div>
                                                <div>
                                                    <i class="fas fa-eye"></i> {{ article.views_count }}
                                                    <span class="mx-2">|</span>
                                                    <i class="fas fa-heart"></i> {{ article.likes_count }}
                                                </div>
                                            </div>
                                        </div>
                                        {% if article.tags.exists %}
                                        <div class="mt-2">
                                            {% for tag in article.tags.all|slice:":4" %}
                                                <a href="{% url 'articles:tag_detail' tag.slug %}" class="tag">#{{ tag.name }}</a>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="تنقل الصفحات" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?q={{ query }}&page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?q={{ query }}&page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?q={{ query }}&page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?q={{ query }}&page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لم يتم العثور على نتائج</h4>
                    {% if query %}
                        <p class="text-muted">لم يتم العثور على أي مقالات تحتوي على "{{ query }}"</p>
                        <div class="mt-4">
                            <h6>اقتراحات للبحث:</h6>
                            <ul class="list-unstyled">
                                <li>• تأكد من صحة الكلمات المكتوبة</li>
                                <li>• جرب كلمات مختلفة أو أكثر عمومية</li>
                                <li>• استخدم كلمات أقل في البحث</li>
                                <li>• جرب البحث في فئة معينة</li>
                            </ul>
                        </div>
                    {% else %}
                        <p class="text-muted">يرجى إدخال كلمات للبحث</p>
                    {% endif %}
                    <a href="{% url 'articles:home' %}" class="btn btn-primary mt-3">
                        <i class="fas fa-home"></i> العودة للرئيسية
                    </a>
                </div>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Popular Searches -->
            <div class="sidebar">
                <h5>عمليات بحث شائعة</h5>
                <div class="d-flex flex-wrap">
                    <a href="?q=أخبار" class="tag">أخبار</a>
                    <a href="?q=رياضة" class="tag">رياضة</a>
                    <a href="?q=تقنية" class="tag">تقنية</a>
                    <a href="?q=اقتصاد" class="tag">اقتصاد</a>
                    <a href="?q=صحة" class="tag">صحة</a>
                    <a href="?q=ثقافة" class="tag">ثقافة</a>
                </div>
            </div>
            
            <!-- Categories -->
            <div class="sidebar">
                <h5>البحث حسب الفئة</h5>
                <div class="list-group list-group-flush">
                    {% for category in categories %}
                    <a href="?category={{ category.id }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <span>
                            {% if category.icon %}
                                <i class="{{ category.icon }}"></i>
                            {% endif %}
                            {{ category.name }}
                        </span>
                        <span class="badge bg-primary rounded-pill">{{ category.articles.count }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Newsletter -->
            <div class="newsletter-section">
                <h3>اشترك في نشرتنا البريدية</h3>
                <p>احصل على آخر الأخبار والمقالات مباشرة في بريدك الإلكتروني</p>
                <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                    {% csrf_token %}
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" name="email" placeholder="بريدك الإلكتروني" required>
                        <button class="btn btn-light" type="submit">اشتراك</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Highlight search terms in results
    const query = '{{ query|escapejs }}';
    if (query) {
        highlightSearchTerms(query);
    }
});

function highlightSearchTerms(query) {
    const terms = query.split(' ').filter(term => term.length > 2);
    const articles = document.querySelectorAll('.article-card');
    
    articles.forEach(article => {
        const title = article.querySelector('.card-title');
        const content = article.querySelector('.card-text');
        
        terms.forEach(term => {
            if (title) {
                title.innerHTML = title.innerHTML.replace(
                    new RegExp(`(${term})`, 'gi'),
                    '<mark>$1</mark>'
                );
            }
            if (content) {
                content.innerHTML = content.innerHTML.replace(
                    new RegExp(`(${term})`, 'gi'),
                    '<mark>$1</mark>'
                );
            }
        });
    });
}
</script>
{% endblock %}