from django.contrib import admin
from django.utils.html import format_html
from .models import Comment, CommentLike

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = [
        'content_preview', 'author', 'article', 'status',
        'is_reply', 'likes_count', 'created_at'
    ]
    list_filter = ['status', 'created_at', 'article__category']
    search_fields = ['content', 'author__first_name', 'author__last_name', 'article__title']
    readonly_fields = ['likes_count', 'created_at', 'updated_at']
    raw_id_fields = ['article', 'parent']
    
    fieldsets = (
        ('معلومات التعليق', {
            'fields': ('article', 'author', 'parent', 'content', 'status')
        }),
        ('الإحصائيات', {
            'fields': ('likes_count',),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'محتوى التعليق'
    
    def is_reply(self, obj):
        return '✓' if obj.parent else '✗'
    is_reply.short_description = 'رد'
    is_reply.boolean = True
    
    actions = ['approve_comments', 'reject_comments', 'mark_as_pending']
    
    def approve_comments(self, request, queryset):
        updated = queryset.update(status='approved')
        self.message_user(request, f'تم الموافقة على {updated} تعليق.')
    approve_comments.short_description = 'الموافقة على التعليقات المحددة'
    
    def reject_comments(self, request, queryset):
        updated = queryset.update(status='rejected')
        self.message_user(request, f'تم رفض {updated} تعليق.')
    reject_comments.short_description = 'رفض التعليقات المحددة'
    
    def mark_as_pending(self, request, queryset):
        updated = queryset.update(status='pending')
        self.message_user(request, f'تم تحويل {updated} تعليق إلى الانتظار.')
    mark_as_pending.short_description = 'تحويل إلى الانتظار'

@admin.register(CommentLike)
class CommentLikeAdmin(admin.ModelAdmin):
    list_display = ['comment_preview', 'user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['comment__content', 'user__first_name', 'user__last_name']
    readonly_fields = ['created_at']
    
    def comment_preview(self, obj):
        return obj.comment.content[:30] + '...' if len(obj.comment.content) > 30 else obj.comment.content
    comment_preview.short_description = 'التعليق'
