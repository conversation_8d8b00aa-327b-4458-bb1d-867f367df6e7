#!/usr/bin/env python
"""
إنشاء بيانات تجريبية لجريدة تاتا نيوز
"""
import os
import sys
import django
from django.conf import settings

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tatanews.settings')
django.setup()

from django.contrib.auth import get_user_model
from articles.models import Category, Tag, Article
from comments.models import Comment
from newsletter.models import Subscriber
from django.utils import timezone
from datetime import timedelta
import random

User = get_user_model()

def create_users():
    """إنشاء المستخدمين"""
    print("إنشاء المستخدمين...")
    
    # إنشاء المدير
    admin, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'مدير',
            'last_name': 'الموقع',
            'role': 'admin',
            'is_staff': True,
            'is_superuser': True,
            'is_verified': True,
        }
    )
    if created:
        admin.set_password('admin123')
        admin.save()
        print(f"تم إنشاء المدير: {admin.username}")
    
    # إنشاء محرر
    editor, created = User.objects.get_or_create(
        username='editor',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'أحمد',
            'last_name': 'المحرر',
            'role': 'editor',
            'is_staff': True,
            'is_verified': True,
        }
    )
    if created:
        editor.set_password('editor123')
        editor.save()
        print(f"تم إنشاء المحرر: {editor.username}")
    
    # إنشاء كتاب
    authors_data = [
        {'username': 'author1', 'first_name': 'سارة', 'last_name': 'الكاتبة', 'email': '<EMAIL>'},
        {'username': 'author2', 'first_name': 'محمد', 'last_name': 'الصحفي', 'email': '<EMAIL>'},
        {'username': 'author3', 'first_name': 'فاطمة', 'last_name': 'الكاتبة', 'email': '<EMAIL>'},
    ]
    
    authors = []
    for author_data in authors_data:
        author, created = User.objects.get_or_create(
            username=author_data['username'],
            defaults={
                'email': author_data['email'],
                'first_name': author_data['first_name'],
                'last_name': author_data['last_name'],
                'role': 'author',
                'is_verified': True,
            }
        )
        if created:
            author.set_password('author123')
            author.save()
            print(f"تم إنشاء الكاتب: {author.username}")
        authors.append(author)
    
    return admin, editor, authors

def create_categories():
    """إنشاء الفئات"""
    print("إنشاء الفئات...")
    
    categories_data = [
        {'name': 'أخبار محلية', 'color': '#007bff', 'icon': 'fas fa-home'},
        {'name': 'أخبار عالمية', 'color': '#28a745', 'icon': 'fas fa-globe'},
        {'name': 'رياضة', 'color': '#ffc107', 'icon': 'fas fa-futbol'},
        {'name': 'تقنية', 'color': '#17a2b8', 'icon': 'fas fa-laptop'},
        {'name': 'اقتصاد', 'color': '#fd7e14', 'icon': 'fas fa-chart-line'},
        {'name': 'صحة', 'color': '#20c997', 'icon': 'fas fa-heartbeat'},
        {'name': 'ثقافة', 'color': '#6f42c1', 'icon': 'fas fa-book'},
        {'name': 'سياحة', 'color': '#e83e8c', 'icon': 'fas fa-plane'},
    ]
    
    categories = []
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={
                'color': cat_data['color'],
                'icon': cat_data['icon'],
                'description': f'قسم {cat_data["name"]} في جريدة تاتا نيوز',
            }
        )
        if created:
            print(f"تم إنشاء الفئة: {category.name}")
        categories.append(category)
    
    return categories

def create_tags():
    """إنشاء العلامات"""
    print("إنشاء العلامات...")
    
    tags_names = [
        'عاجل', 'مهم', 'حصري', 'تحليل', 'رأي', 'تقرير', 'مقابلة',
        'السعودية', 'الإمارات', 'مصر', 'الأردن', 'المغرب',
        'كرة القدم', 'كرة السلة', 'تنس', 'سباحة',
        'ذكي اصطناعي', 'تطبيقات', 'أمن سيبراني', 'بلوك تشين',
        'استثمار', 'أسهم', 'عملات رقمية', 'تجارة',
        'كوفيد', 'لقاحات', 'طب', 'تغذية',
        'أدب', 'شعر', 'مسرح', 'سينما', 'موسيقى',
        'سفر', 'فنادق', 'مطاعم', 'معالم'
    ]
    
    tags = []
    for tag_name in tags_names:
        tag, created = Tag.objects.get_or_create(name=tag_name)
        if created:
            print(f"تم إنشاء العلامة: {tag.name}")
        tags.append(tag)
    
    return tags

def create_articles(authors, categories, tags):
    """إنشاء المقالات"""
    print("إنشاء المقالات...")
    
    articles_data = [
        {
            'title': 'افتتاح مشروع نيوم الضخم في المملكة العربية السعودية',
            'content': '''
            <p>شهدت المملكة العربية السعودية اليوم افتتاح المرحلة الأولى من مشروع نيوم الضخم، والذي يعد أحد أكبر المشاريع التنموية في العالم.</p>
            
            <p>يهدف المشروع إلى إنشاء مدينة مستقبلية تعتمد على التقنيات الحديثة والطاقة المتجددة، وتوفر فرص عمل لآلاف الأشخاص.</p>
            
            <h3>مميزات المشروع</h3>
            <ul>
                <li>استخدام الطاقة المتجددة بنسبة 100%</li>
                <li>تقنيات الذكاء الاصطناعي في جميع الخدمات</li>
                <li>بيئة معيشية مستدامة</li>
                <li>مركز عالمي للأعمال والسياحة</li>
            </ul>
            
            <p>من المتوقع أن يساهم هذا المشروع في تحقيق رؤية المملكة 2030 وتنويع الاقتصاد السعودي.</p>
            ''',
            'category': 'أخبار محلية',
            'tags': ['السعودية', 'مهم', 'تقرير'],
            'is_featured': True,
            'is_breaking_news': True,
        },
        {
            'title': 'تطوير تقنية جديدة للذكاء الاصطناعي في الطب',
            'content': '''
            <p>أعلن فريق من الباحثين في جامعة الملك عبدالله للعلوم والتقنية عن تطوير تقنية جديدة للذكاء الاصطناعي يمكنها تشخيص الأمراض بدقة عالية.</p>
            
            <p>تعتمد التقنية الجديدة على تحليل الصور الطبية باستخدام خوارزميات التعلم العميق، مما يساعد الأطباء في اتخاذ قرارات أكثر دقة.</p>
            
            <h3>فوائد التقنية الجديدة</h3>
            <p>تتميز هذه التقنية بعدة فوائد مهمة:</p>
            <ul>
                <li>دقة تشخيص تصل إلى 95%</li>
                <li>سرعة في التحليل والنتائج</li>
                <li>تقليل الأخطاء البشرية</li>
                <li>توفير التكاليف الطبية</li>
            </ul>
            ''',
            'category': 'تقنية',
            'tags': ['ذكي اصطناعي', 'طب', 'تحليل'],
            'is_featured': True,
        },
        {
            'title': 'منتخب السعودية يحقق فوزاً تاريخياً في كأس العالم',
            'content': '''
            <p>حقق المنتخب السعودي لكرة القدم فوزاً تاريخياً أمس في مباراة مهمة ضمن تصفيات كأس العالم.</p>
            
            <p>جاء الفوز بنتيجة 3-1 في مباراة مثيرة شهدت أداءً متميزاً من اللاعبين السعوديين.</p>
            
            <p>هذا الفوز يضع المنتخب السعودي في موقع متقدم في جدول التصفيات ويقربه من التأهل المباشر لكأس العالم.</p>
            ''',
            'category': 'رياضة',
            'tags': ['كرة القدم', 'السعودية', 'عاجل'],
        },
        {
            'title': 'ارتفاع أسعار النفط عالمياً بنسبة 5%',
            'content': '''
            <p>شهدت أسواق النفط العالمية ارتفاعاً ملحوظاً في الأسعار خلال تداولات اليوم، حيث سجل خام برنت ارتفاعاً بنسبة 5%.</p>
            
            <p>يأتي هذا الارتفاع نتيجة لعدة عوامل اقتصادية وجيوسياسية تؤثر على العرض والطلب في الأسواق العالمية.</p>
            
            <h3>أسباب الارتفاع</h3>
            <ul>
                <li>انخفاض المخزونات الأمريكية</li>
                <li>زيادة الطلب من الصين</li>
                <li>التوترات الجيوسياسية</li>
                <li>قرارات أوبك+</li>
            </ul>
            ''',
            'category': 'اقتصاد',
            'tags': ['استثمار', 'تحليل', 'مهم'],
        },
        {
            'title': 'اكتشاف علاج جديد لمرض السكري',
            'content': '''
            <p>أعلن فريق من الباحثين في كلية الطب بجامعة الملك سعود عن اكتشاف علاج جديد لمرض السكري من النوع الثاني.</p>
            
            <p>يعتمد العلاج الجديد على استخدام الخلايا الجذعية لإعادة تجديد خلايا البنكرياس المسؤولة عن إنتاج الأنسولين.</p>
            
            <p>أظهرت التجارب الأولية نتائج مبشرة، حيث تمكن 80% من المرضى من التحكم في مستوى السكر بدون أدوية.</p>
            ''',
            'category': 'صحة',
            'tags': ['طب', 'حصري', 'مهم'],
            'is_featured': True,
        },
        {
            'title': 'معرض الرياض للكتاب يستقبل مليون زائر',
            'content': '''
            <p>أعلنت إدارة معرض الرياض الدولي للكتاب أن عدد الزوار تجاوز المليون زائر منذ افتتاحه قبل أسبوعين.</p>
            
            <p>يضم المعرض هذا العام أكثر من 500 دار نشر من 30 دولة، ويقدم فعاليات ثقافية متنوعة.</p>
            
            <p>شهد المعرض إقبالاً كبيراً من جميع فئات المجتمع، خاصة الشباب والطلاب.</p>
            ''',
            'category': 'ثقافة',
            'tags': ['أدب', 'ثقافة', 'السعودية'],
        }
    ]
    
    articles = []
    for i, article_data in enumerate(articles_data):
        # البحث عن الفئة
        category = next((cat for cat in categories if cat.name == article_data['category']), categories[0])
        
        # اختيار كاتب عشوائي
        author = random.choice(authors)
        
        # إنشاء المقال
        article, created = Article.objects.get_or_create(
            title=article_data['title'],
            defaults={
                'content': article_data['content'],
                'excerpt': article_data['content'][:200] + '...',
                'author': author,
                'category': category,
                'status': 'published',
                'published_at': timezone.now() - timedelta(days=random.randint(0, 30)),
                'is_featured': article_data.get('is_featured', False),
                'is_breaking_news': article_data.get('is_breaking_news', False),
                'views_count': random.randint(100, 5000),
                'likes_count': random.randint(10, 500),
            }
        )
        
        if created:
            # إضافة العلامات
            article_tags = [tag for tag in tags if tag.name in article_data['tags']]
            article.tags.set(article_tags)
            
            print(f"تم إنشاء المقال: {article.title}")
            articles.append(article)
    
    return articles

def create_comments(articles, authors):
    """إنشاء التعليقات"""
    print("إنشاء التعليقات...")
    
    comments_data = [
        'مقال رائع ومفيد جداً، شكراً لك',
        'معلومات قيمة، أتطلع للمزيد',
        'موضوع مهم يستحق المتابعة',
        'شكراً على هذا التحليل المتميز',
        'مقال ممتاز، استفدت منه كثيراً',
        'معلومات دقيقة ومفصلة',
        'موضوع شيق ومكتوب بأسلوب جميل',
        'أحسنت في اختيار الموضوع',
        'مقال يستحق القراءة والمشاركة',
        'معلومات مفيدة ومحدثة'
    ]
    
    for article in articles:
        # إنشاء تعليقات عشوائية لكل مقال
        num_comments = random.randint(2, 8)
        for i in range(num_comments):
            comment_content = random.choice(comments_data)
            comment_author = random.choice(authors)
            
            comment, created = Comment.objects.get_or_create(
                article=article,
                author=comment_author,
                content=comment_content,
                defaults={
                    'status': 'approved',
                    'created_at': timezone.now() - timedelta(days=random.randint(0, 10)),
                    'likes_count': random.randint(0, 50),
                }
            )
            
            if created:
                print(f"تم إنشاء تعليق على: {article.title[:30]}...")

def create_subscribers():
    """إنشاء مشتركين في النشرة البريدية"""
    print("إنشاء مشتركين في النشرة البريدية...")
    
    subscribers_data = [
        {'email': '<EMAIL>', 'name': 'أحمد محمد'},
        {'email': '<EMAIL>', 'name': 'فاطمة علي'},
        {'email': '<EMAIL>', 'name': 'محمد أحمد'},
        {'email': '<EMAIL>', 'name': 'سارة محمود'},
        {'email': '<EMAIL>', 'name': 'علي حسن'},
    ]
    
    for sub_data in subscribers_data:
        subscriber, created = Subscriber.objects.get_or_create(
            email=sub_data['email'],
            defaults={
                'name': sub_data['name'],
                'is_active': True,
                'daily_digest': True,
                'weekly_digest': True,
                'breaking_news': True,
            }
        )
        
        if created:
            print(f"تم إنشاء مشترك: {subscriber.email}")

def main():
    """الدالة الرئيسية"""
    print("بدء إنشاء البيانات التجريبية...")
    
    # إنشاء المستخدمين
    admin, editor, authors = create_users()
    
    # إنشاء الفئات
    categories = create_categories()
    
    # إنشاء العلامات
    tags = create_tags()
    
    # إنشاء المقالات
    articles = create_articles(authors, categories, tags)
    
    # إنشاء التعليقات
    create_comments(articles, authors + [admin, editor])
    
    # إنشاء المشتركين
    create_subscribers()
    
    print("\n" + "="*50)
    print("تم إنشاء البيانات التجريبية بنجاح!")
    print("="*50)
    print("بيانات تسجيل الدخول:")
    print("المدير: admin / admin123")
    print("المحرر: editor / editor123")
    print("الكاتب: author1 / author123")
    print("="*50)

if __name__ == '__main__':
    main()