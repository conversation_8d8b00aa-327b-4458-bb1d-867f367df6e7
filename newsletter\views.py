from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
import hashlib
import uuid

from .models import Subscriber, Newsletter
from .forms import SubscriberForm, PreferencesForm

@require_POST
def subscribe(request):
    """الاشتراك في النشرة البريدية"""
    try:
        email = request.POST.get('email', '').strip().lower()
        name = request.POST.get('name', '').strip()
        
        if not email:
            return JsonResponse({
                'success': False,
                'message': 'يرجى إدخال البريد الإلكتروني'
            })
        
        # التحقق من وجود المشترك
        subscriber, created = Subscriber.objects.get_or_create(
            email=email,
            defaults={'name': name, 'is_active': True}
        )
        
        if not created:
            if subscriber.is_active:
                return JsonResponse({
                    'success': False,
                    'message': 'هذا البريد الإلكتروني مشترك بالفعل'
                })
            else:
                # إعادة تفعيل الاشتراك
                subscriber.is_active = True
                subscriber.unsubscribed_at = None
                subscriber.save()
        
        # إرسال بريد تأكيد الاشتراك
        send_confirmation_email(subscriber)
        
        return JsonResponse({
            'success': True,
            'message': 'تم الاشتراك بنجاح! تحقق من بريدك الإلكتروني لتأكيد الاشتراك.'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'حدث خطأ أثناء الاشتراك. يرجى المحاولة مرة أخرى.'
        })

def unsubscribe(request, email):
    """إلغاء الاشتراك"""
    try:
        subscriber = get_object_or_404(Subscriber, email=email)
        
        if request.method == 'POST':
            subscriber.unsubscribe()
            messages.success(request, 'تم إلغاء الاشتراك بنجاح!')
            return redirect('articles:home')
        
        context = {
            'subscriber': subscriber,
        }
        return render(request, 'newsletter/unsubscribe.html', context)
        
    except Exception as e:
        messages.error(request, 'حدث خطأ أثناء إلغاء الاشتراك.')
        return redirect('articles:home')

def confirm_subscription(request, token):
    """تأكيد الاشتراك"""
    try:
        # البحث عن المشترك باستخدام التوكن
        # هذا مثال بسيط، يمكن تحسينه بنظام توكن أكثر أماناً
        subscriber = get_object_or_404(Subscriber, email__icontains=token[:10])
        
        if not subscriber.is_active:
            subscriber.is_active = True
            subscriber.save()
            messages.success(request, 'تم تأكيد الاشتراك بنجاح!')
        else:
            messages.info(request, 'تم تأكيد الاشتراك مسبقاً.')
        
        return redirect('articles:home')
        
    except Exception as e:
        messages.error(request, 'رابط التأكيد غير صحيح أو منتهي الصلاحية.')
        return redirect('articles:home')

def preferences(request, email):
    """تفضيلات الاشتراك"""
    try:
        subscriber = get_object_or_404(Subscriber, email=email)
        
        if request.method == 'POST':
            form = PreferencesForm(request.POST, instance=subscriber)
            if form.is_valid():
                form.save()
                messages.success(request, 'تم حفظ التفضيلات بنجاح!')
                return redirect('articles:home')
        else:
            form = PreferencesForm(instance=subscriber)
        
        context = {
            'form': form,
            'subscriber': subscriber,
        }
        return render(request, 'newsletter/preferences.html', context)
        
    except Exception as e:
        messages.error(request, 'حدث خطأ أثناء تحديث التفضيلات.')
        return redirect('articles:home')

def send_confirmation_email(subscriber):
    """إرسال بريد تأكيد الاشتراك"""
    try:
        # إنشاء توكن بسيط للتأكيد
        token = hashlib.md5(f"{subscriber.email}{uuid.uuid4()}".encode()).hexdigest()
        
        subject = 'تأكيد الاشتراك في نشرة تاتا نيوز'
        
        html_message = render_to_string('newsletter/confirmation_email.html', {
            'subscriber': subscriber,
            'confirmation_url': f"{settings.SITE_URL}/newsletter/confirm/{token}/",
            'unsubscribe_url': f"{settings.SITE_URL}/newsletter/unsubscribe/{subscriber.email}/",
        })
        
        plain_message = strip_tags(html_message)
        
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[subscriber.email],
            html_message=html_message,
            fail_silently=False,
        )
        
    except Exception as e:
        print(f"خطأ في إرسال بريد التأكيد: {e}")

def newsletter_archive(request):
    """أرشيف النشرات البريدية"""
    newsletters = Newsletter.objects.filter(
        status='sent'
    ).order_by('-sent_at')
    
    context = {
        'newsletters': newsletters,
    }
    return render(request, 'newsletter/archive.html', context)

def newsletter_detail(request, newsletter_id):
    """تفاصيل النشرة البريدية"""
    newsletter = get_object_or_404(Newsletter, id=newsletter_id, status='sent')
    
    context = {
        'newsletter': newsletter,
    }
    return render(request, 'newsletter/detail.html', context)
