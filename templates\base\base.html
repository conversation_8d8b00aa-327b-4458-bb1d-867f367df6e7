<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block meta_description %}جريدة تاتا نيوز - آخر الأخبار والمقالات{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}أخبار، مقالات، جريدة، تاتا نيوز{% endblock %}">
    <meta name="author" content="تاتا نيوز">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}تاتا نيوز{% endblock %}">
    <meta property="og:description" content="{% block og_description %}جريدة تاتا نيوز - آخر الأخبار والمقالات{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% load static %}{% static 'images/logo-og.png' %}{% endblock %}">
    <meta property="og:url" content="{% block og_url %}{{ request.build_absolute_uri }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    
    <title>{% block title %}تاتا نيوز - جريدة إلكترونية{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Amiri:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/main.css' %}">
    
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
</head>
<body class="{% block body_class %}{% endblock %}">
    
    <!-- Header -->
    <header class="header-main">
        <!-- Top Bar -->
        <div class="top-bar bg-dark text-white py-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span id="current-date"></span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="social-links">
                            <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="text-white me-3"><i class="fab fa-youtube"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Header -->
        <div class="main-header bg-white shadow-sm">
            <div class="container">
                <div class="row align-items-center py-3">
                    <div class="col-lg-3">
                        <div class="logo">
                            <a href="{% url 'articles:home' %}" class="text-decoration-none">
                                <h1 class="mb-0 text-primary fw-bold">تاتا نيوز</h1>
                                <small class="text-muted">جريدة إلكترونية شاملة</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <!-- Search Form -->
                        <form class="search-form" method="GET" action="{% url 'articles:search' %}">
                            <div class="input-group">
                                <input type="text" class="form-control" name="q" placeholder="ابحث في الأخبار..." value="{{ request.GET.q }}">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="col-lg-3 text-end">
                        {% if user.is_authenticated %}
                            <div class="dropdown">
                                <a class="btn btn-outline-primary dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-user me-1"></i>
                                    {{ user.get_full_name|default:user.username }}
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'accounts:profile' %}">الملف الشخصي</a></li>
                                    {% if user.can_publish %}
                                        <li><a class="dropdown-item" href="{% url 'articles:create' %}">إضافة مقال</a></li>
                                    {% endif %}
                                    {% if user.is_staff %}
                                        <li><a class="dropdown-item" href="/admin/">لوحة الإدارة</a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'account_logout' %}">تسجيل الخروج</a></li>
                                </ul>
                            </div>
                        {% else %}
                            <a href="{% url 'account_login' %}" class="btn btn-outline-primary me-2">تسجيل الدخول</a>
                            <a href="{% url 'account_signup' %}" class="btn btn-primary">إنشاء حساب</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'articles:home' %}">الرئيسية</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                الأقسام
                            </a>
                            <ul class="dropdown-menu">
                                {% for category in categories %}
                                    <li><a class="dropdown-item" href="{{ category.get_absolute_url }}">{{ category.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'articles:latest' %}">آخر الأخبار</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'articles:featured' %}">المقالات المميزة</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'newsletter:subscribe' %}">النشرة البريدية</a>
                        </li>
                    </ul>
                    
                    <!-- Breaking News Ticker -->
                    {% if breaking_news %}
                        <div class="breaking-news d-none d-lg-block">
                            <span class="badge bg-danger me-2">عاجل</span>
                            <marquee behavior="scroll" direction="right" class="text-white">
                                {% for news in breaking_news %}
                                    {{ news.title }} • 
                                {% endfor %}
                            </marquee>
                        </div>
                    {% endif %}
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Main Content -->
    <main class="main-content">
        {% if messages %}
            <div class="container mt-3">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="footer bg-dark text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="mb-3">تاتا نيوز</h5>
                    <p class="text-muted">
                        جريدة إلكترونية شاملة تقدم آخر الأخبار والمقالات في جميع المجالات
                        بأسلوب مهني ومتميز.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3 fs-4"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 mb-4">
                    <h6 class="mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'articles:home' %}" class="text-muted text-decoration-none">الرئيسية</a></li>
                        <li><a href="{% url 'articles:latest' %}" class="text-muted text-decoration-none">آخر الأخبار</a></li>
                        <li><a href="{% url 'articles:featured' %}" class="text-muted text-decoration-none">المقالات المميزة</a></li>
                        <li><a href="#" class="text-muted text-decoration-none">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3">الأقسام</h6>
                    <ul class="list-unstyled">
                        {% for category in categories|slice:":5" %}
                            <li><a href="{{ category.get_absolute_url }}" class="text-muted text-decoration-none">{{ category.name }}</a></li>
                        {% endfor %}
                    </ul>
                </div>
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3">النشرة البريدية</h6>
                    <p class="text-muted small">اشترك في نشرتنا البريدية لتصلك آخر الأخبار</p>
                    <form method="POST" action="{% url 'newsletter:subscribe' %}">
                        {% csrf_token %}
                        <div class="input-group mb-3">
                            <input type="email" class="form-control" name="email" placeholder="بريدك الإلكتروني" required>
                            <button class="btn btn-primary" type="submit">اشتراك</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">&copy; 2024 تاتا نيوز. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="#" class="text-muted text-decoration-none me-3">سياسة الخصوصية</a>
                    <a href="#" class="text-muted text-decoration-none">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary position-fixed bottom-0 end-0 m-3 rounded-circle" style="display: none; z-index: 1000;">
        <i class="fas fa-arrow-up"></i>
    </button>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    </script>
</body>
</html>