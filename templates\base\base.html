<!DOCTYPE html>
<html lang="ar" dir="rtl" data-bs-theme="auto">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="{% block meta_description %}جريدة تاتا نيوز - آخر الأخبار والمقالات مع أحدث التصاميم العصرية{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}أخبار، مقالات، جريدة، تاتا نيوز، أخبار عربية، صحافة{% endblock %}">
    <meta name="author" content="تاتا نيوز">
    <meta name="theme-color" content="#2563eb">
    <meta name="color-scheme" content="light dark">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}تاتا نيوز{% endblock %}">
    <meta property="og:description" content="{% block og_description %}جريدة تاتا نيوز - آخر الأخبار والمقالات مع أحدث التصاميم العصرية{% endblock %}">
    <meta property="og:image" content="{% block og_image %}{% load static %}{% static 'images/logo-og.png' %}{% endblock %}">
    <meta property="og:url" content="{% block og_url %}{{ request.build_absolute_uri }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    <meta property="og:site_name" content="تاتا نيوز">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}تاتا نيوز{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}جريدة تاتا نيوز - آخر الأخبار والمقالات{% endblock %}">
    <meta name="twitter:image" content="{% block twitter_image %}{% load static %}{% static 'images/logo-og.png' %}{% endblock %}">
    <meta name="twitter:creator" content="@tatanews">

    <title>{% block title %}تاتا نيوز - جريدة إلكترونية{% endblock %}</title>

    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="{% static 'css/modern.css' %}" as="style">

    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet" integrity="sha384-nU14brUcp6StFntEOOEBvcJm4huWjB0OcIeQ3fltAfSmuZFrkAif0T+UtNGlKKQv" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

    <!-- Modern Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/modern.css' %}">
    <link rel="stylesheet" href="{% static 'css/main.css' %}">

    {% block extra_css %}{% endblock %}

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'images/apple-touch-icon.png' %}">
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'images/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'images/favicon-16x16.png' %}">
    <link rel="manifest" href="{% static 'manifest.json' %}">

    <!-- Theme Detection Script -->
    <script>
        // Theme detection and setting
        (function() {
            const getStoredTheme = () => localStorage.getItem('theme');
            const setStoredTheme = theme => localStorage.setItem('theme', theme);
            const getPreferredTheme = () => {
                const storedTheme = getStoredTheme();
                if (storedTheme) return storedTheme;
                return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            };
            const setTheme = theme => {
                if (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    document.documentElement.setAttribute('data-bs-theme', 'dark');
                } else {
                    document.documentElement.setAttribute('data-bs-theme', theme);
                }
            };
            setTheme(getPreferredTheme());
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                const storedTheme = getStoredTheme();
                if (storedTheme !== 'light' && storedTheme !== 'dark') {
                    setTheme(getPreferredTheme());
                }
            });
        })();
    </script>
</head>
<body class="{% block body_class %}{% endblock %}">
    
    <!-- Modern Header -->
    <header class="modern-nav">
        <!-- Top Bar -->
        <div class="top-bar" style="background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%); color: white; padding: 0.5rem 0;">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-calendar-alt me-2 text-primary-200"></i>
                            <span id="current-date" class="text-sm"></span>
                            <span class="mx-3 text-primary-200">|</span>
                            <i class="fas fa-thermometer-half me-1 text-primary-200"></i>
                            <span class="text-sm">القاهرة 25°</span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex align-items-center justify-content-end gap-3">
                            <!-- Theme Toggle -->
                            <button id="theme-toggle" class="btn btn-sm btn-outline-light border-0" title="تغيير المظهر">
                                <i class="fas fa-moon" id="theme-icon"></i>
                            </button>
                            <!-- Social Links -->
                            <div class="social-links d-flex gap-2">
                                <a href="#" class="text-white hover-scale" title="فيسبوك">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="text-white hover-scale" title="تويتر">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="#" class="text-white hover-scale" title="إنستغرام">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <a href="#" class="text-white hover-scale" title="يوتيوب">
                                    <i class="fab fa-youtube"></i>
                                </a>
                                <a href="#" class="text-white hover-scale" title="تيليجرام">
                                    <i class="fab fa-telegram"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Header -->
        <div class="main-header py-3">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-3 col-md-4">
                        <div class="logo">
                            <a href="{% url 'articles:home' %}" class="text-decoration-none d-flex align-items-center">
                                <div class="logo-icon me-2">
                                    <div class="d-flex align-items-center justify-content-center"
                                         style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: var(--radius-lg); color: white; font-size: 1.5rem; font-weight: bold;">
                                        ت
                                    </div>
                                </div>
                                <div>
                                    <h1 class="mb-0 fw-bold" style="font-size: 1.75rem; background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                                        تاتا نيوز
                                    </h1>
                                    <small class="text-muted d-block" style="font-size: 0.75rem;">جريدة إلكترونية عصرية</small>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-5">
                        <!-- Modern Search Form -->
                        <form class="search-form position-relative" method="GET" action="{% url 'articles:search' %}">
                            <div class="modern-search-container">
                                <input type="text"
                                       class="modern-input w-100 pe-5"
                                       name="q"
                                       placeholder="ابحث في الأخبار والمقالات..."
                                       value="{{ request.GET.q }}"
                                       style="padding-right: 3rem;">
                                <button class="btn position-absolute top-50 end-0 translate-middle-y me-2 border-0 bg-transparent"
                                        type="submit"
                                        style="color: var(--primary-600);">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="col-lg-3 col-md-3 text-end">
                        {% if user.is_authenticated %}
                            <div class="dropdown">
                                <button class="btn btn-modern-secondary dropdown-toggle border-0"
                                        type="button"
                                        data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                    <div class="d-flex align-items-center">
                                        <div class="user-avatar me-2"
                                             style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.875rem; font-weight: 600;">
                                            {{ user.get_full_name.0|default:user.username.0|upper }}
                                        </div>
                                        <span class="d-none d-md-inline">{{ user.get_full_name|default:user.username|truncatechars:15 }}</span>
                                    </div>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end modern-card border-0 mt-2">
                                    <li><a class="dropdown-item d-flex align-items-center" href="{% url 'accounts:profile' %}">
                                        <i class="fas fa-user me-2 text-primary"></i>الملف الشخصي
                                    </a></li>
                                    {% if user.can_publish %}
                                        <li><a class="dropdown-item d-flex align-items-center" href="{% url 'articles:create' %}">
                                            <i class="fas fa-plus me-2 text-success"></i>إضافة مقال
                                        </a></li>
                                    {% endif %}
                                    {% if user.is_staff %}
                                        <li><a class="dropdown-item d-flex align-items-center" href="/admin/">
                                            <i class="fas fa-cog me-2 text-warning"></i>لوحة الإدارة
                                        </a></li>
                                    {% endif %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item d-flex align-items-center text-danger" href="{% url 'account_logout' %}">
                                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                    </a></li>
                                </ul>
                            </div>
                        {% else %}
                            <div class="d-flex gap-2">
                                <a href="{% url 'account_login' %}" class="btn btn-modern-secondary">تسجيل الدخول</a>
                                <a href="{% url 'account_signup' %}" class="btn btn-modern-primary">إنشاء حساب</a>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Navigation -->
        <nav class="modern-navigation" style="background: var(--gradient-primary); border-bottom: 1px solid rgba(255,255,255,0.1);">
            <div class="container">
                <div class="d-flex align-items-center justify-content-between py-2">
                    <!-- Mobile Menu Toggle -->
                    <button class="btn btn-link text-white d-lg-none border-0 p-0"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#mobileNav"
                            aria-controls="mobileNav"
                            aria-expanded="false">
                        <i class="fas fa-bars fs-5"></i>
                    </button>

                    <!-- Desktop Navigation -->
                    <ul class="nav d-none d-lg-flex align-items-center mb-0">
                        <li class="nav-item">
                            <a class="nav-link text-white px-3 py-2 rounded hover-lift" href="{% url 'articles:home' %}">
                                <i class="fas fa-home me-1"></i>الرئيسية
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle text-white px-3 py-2 rounded hover-lift"
                               href="#"
                               role="button"
                               data-bs-toggle="dropdown"
                               aria-expanded="false">
                                <i class="fas fa-list me-1"></i>الأقسام
                            </a>
                            <ul class="dropdown-menu modern-card border-0 mt-2">
                                {% for category in categories %}
                                    <li>
                                        <a class="dropdown-item d-flex align-items-center hover-lift" href="{{ category.get_absolute_url }}">
                                            {% if category.icon %}
                                                <i class="{{ category.icon }} me-2 text-primary"></i>
                                            {% else %}
                                                <i class="fas fa-folder me-2 text-primary"></i>
                                            {% endif %}
                                            {{ category.name }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white px-3 py-2 rounded hover-lift" href="{% url 'articles:latest' %}">
                                <i class="fas fa-clock me-1"></i>آخر الأخبار
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white px-3 py-2 rounded hover-lift" href="{% url 'articles:featured' %}">
                                <i class="fas fa-star me-1"></i>المقالات المميزة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white px-3 py-2 rounded hover-lift" href="{% url 'newsletter:subscribe' %}">
                                <i class="fas fa-envelope me-1"></i>النشرة البريدية
                            </a>
                        </li>
                    </ul>

                    <!-- Breaking News Ticker -->
                    {% if breaking_news %}
                        <div class="breaking-news-ticker d-none d-xl-flex align-items-center" style="max-width: 400px;">
                            <span class="badge bg-danger me-2 px-2 py-1">
                                <i class="fas fa-bolt me-1"></i>عاجل
                            </span>
                            <div class="ticker-content text-white text-truncate">
                                <marquee behavior="scroll" direction="right" scrollamount="3">
                                    {% for news in breaking_news|slice:":3" %}
                                        <a href="{{ news.get_absolute_url }}" class="text-white text-decoration-none me-4">
                                            {{ news.title }}
                                        </a>
                                    {% endfor %}
                                </marquee>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Mobile Navigation -->
                <div class="collapse" id="mobileNav">
                    <div class="py-3 border-top border-white border-opacity-25">
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link text-white py-2" href="{% url 'articles:home' %}">
                                    <i class="fas fa-home me-2"></i>الرئيسية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white py-2" href="{% url 'articles:latest' %}">
                                    <i class="fas fa-clock me-2"></i>آخر الأخبار
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link text-white py-2" href="{% url 'articles:featured' %}">
                                    <i class="fas fa-star me-2"></i>المقالات المميزة
                                </a>
                            </li>
                            {% for category in categories|slice:":5" %}
                                <li class="nav-item">
                                    <a class="nav-link text-white py-2 ps-4" href="{{ category.get_absolute_url }}">
                                        {% if category.icon %}
                                            <i class="{{ category.icon }} me-2"></i>
                                        {% else %}
                                            <i class="fas fa-folder me-2"></i>
                                        {% endif %}
                                        {{ category.name }}
                                    </a>
                                </li>
                            {% endfor %}
                            <li class="nav-item">
                                <a class="nav-link text-white py-2" href="{% url 'newsletter:subscribe' %}">
                                    <i class="fas fa-envelope me-2"></i>النشرة البريدية
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Breaking News Alert (Mobile) -->
    {% if breaking_news %}
        <div class="breaking-news-mobile d-xl-none alert alert-danger mb-0 rounded-0 border-0" style="background: var(--accent-red);">
            <div class="container">
                <div class="d-flex align-items-center">
                    <span class="badge bg-white text-danger me-2">
                        <i class="fas fa-bolt"></i>
                    </span>
                    <div class="flex-grow-1">
                        <marquee behavior="scroll" direction="right" scrollamount="2" class="text-white">
                            {% for news in breaking_news|slice:":3" %}
                                <a href="{{ news.get_absolute_url }}" class="text-white text-decoration-none me-4">
                                    {{ news.title }}
                                </a>
                            {% endfor %}
                        </marquee>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="main-content" style="min-height: calc(100vh - 200px);">
        {% if messages %}
            <div class="container mt-3">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show modern-card border-0" role="alert">
                        <div class="d-flex align-items-center">
                            {% if message.tags == 'success' %}
                                <i class="fas fa-check-circle me-2 text-success"></i>
                            {% elif message.tags == 'error' %}
                                <i class="fas fa-exclamation-circle me-2 text-danger"></i>
                            {% elif message.tags == 'warning' %}
                                <i class="fas fa-exclamation-triangle me-2 text-warning"></i>
                            {% else %}
                                <i class="fas fa-info-circle me-2 text-info"></i>
                            {% endif %}
                            {{ message }}
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <!-- Modern Footer -->
    <footer class="modern-footer" style="background: linear-gradient(135deg, var(--neutral-800) 0%, var(--neutral-900) 100%); color: white;">
        <div class="container py-5">
            <div class="row g-4">
                <!-- Brand Section -->
                <div class="col-lg-4 mb-4">
                    <div class="footer-brand">
                        <div class="d-flex align-items-center mb-3">
                            <div class="logo-icon me-3">
                                <div class="d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px; background: var(--gradient-primary); border-radius: var(--radius-lg); color: white; font-size: 1.5rem; font-weight: bold;">
                                    ت
                                </div>
                            </div>
                            <div>
                                <h5 class="mb-0 fw-bold">تاتا نيوز</h5>
                                <small class="text-muted">جريدة إلكترونية عصرية</small>
                            </div>
                        </div>
                        <p class="text-muted mb-4">
                            جريدة إلكترونية شاملة تقدم آخر الأخبار والمقالات في جميع المجالات
                            بأسلوب مهني ومتميز مع أحدث التقنيات والتصاميم العصرية.
                        </p>
                        <div class="social-links d-flex gap-3">
                            <a href="#" class="social-link" title="فيسبوك">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link" title="تويتر">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link" title="إنستغرام">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="social-link" title="يوتيوب">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="#" class="social-link" title="تيليجرام">
                                <i class="fab fa-telegram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="mb-3 fw-semibold">روابط سريعة</h6>
                    <ul class="list-unstyled footer-links">
                        <li><a href="{% url 'articles:home' %}"><i class="fas fa-home me-2"></i>الرئيسية</a></li>
                        <li><a href="{% url 'articles:latest' %}"><i class="fas fa-clock me-2"></i>آخر الأخبار</a></li>
                        <li><a href="{% url 'articles:featured' %}"><i class="fas fa-star me-2"></i>المقالات المميزة</a></li>
                        <li><a href="#"><i class="fas fa-envelope me-2"></i>اتصل بنا</a></li>
                        <li><a href="#"><i class="fas fa-info-circle me-2"></i>من نحن</a></li>
                    </ul>
                </div>

                <!-- Categories -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6 class="mb-3 fw-semibold">الأقسام الرئيسية</h6>
                    <ul class="list-unstyled footer-links">
                        {% for category in categories|slice:":6" %}
                            <li>
                                <a href="{{ category.get_absolute_url }}">
                                    {% if category.icon %}
                                        <i class="{{ category.icon }} me-2"></i>
                                    {% else %}
                                        <i class="fas fa-folder me-2"></i>
                                    {% endif %}
                                    {{ category.name }}
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>

                <!-- Newsletter -->
                <div class="col-lg-3 mb-4">
                    <h6 class="mb-3 fw-semibold">النشرة البريدية</h6>
                    <p class="text-muted small mb-3">
                        اشترك في نشرتنا البريدية لتصلك آخر الأخبار والمقالات مباشرة إلى بريدك الإلكتروني
                    </p>
                    <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                        {% csrf_token %}
                        <div class="input-group mb-3">
                            <input type="email"
                                   class="modern-input border-0"
                                   name="email"
                                   placeholder="بريدك الإلكتروني"
                                   required
                                   style="background: rgba(255,255,255,0.1); color: white;">
                            <button class="btn btn-modern-primary border-0" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                    <div class="app-download mt-3">
                        <small class="text-muted d-block mb-2">حمل التطبيق</small>
                        <div class="d-flex gap-2">
                            <a href="#" class="btn btn-sm btn-outline-light">
                                <i class="fab fa-apple me-1"></i>iOS
                            </a>
                            <a href="#" class="btn btn-sm btn-outline-light">
                                <i class="fab fa-google-play me-1"></i>Android
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <hr class="my-4 border-secondary">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-muted small">
                        &copy; 2024 تاتا نيوز. جميع الحقوق محفوظة.
                        <span class="text-primary">صُنع بـ ❤️ في مصر</span>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-legal">
                        <a href="#" class="text-muted text-decoration-none me-3 small hover-lift">سياسة الخصوصية</a>
                        <a href="#" class="text-muted text-decoration-none me-3 small hover-lift">شروط الاستخدام</a>
                        <a href="#" class="text-muted text-decoration-none small hover-lift">ملفات تعريف الارتباط</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Modern Back to Top Button -->
    <button id="backToTop"
            class="btn position-fixed bottom-0 end-0 m-4 rounded-circle shadow-lg"
            style="display: none; z-index: 1000; width: 50px; height: 50px; background: var(--gradient-primary); border: none; color: white;"
            title="العودة للأعلى">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Reading Progress Bar -->
    <div id="reading-progress"
         class="position-fixed top-0 start-0 bg-primary"
         style="height: 3px; z-index: 1001; width: 0%; transition: width 0.3s ease;"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
            integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
            crossorigin="anonymous"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/modern.js' %}"></script>
    <script src="{% static 'js/main.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // Initialize modern features
        document.addEventListener('DOMContentLoaded', function() {
            // Set current date with modern formatting
            const currentDate = document.getElementById('current-date');
            if (currentDate) {
                const now = new Date();
                const options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                currentDate.textContent = now.toLocaleDateString('ar-SA', options);
            }

            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = document.getElementById('theme-icon');

            if (themeToggle && themeIcon) {
                // Set initial icon based on current theme
                const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                themeIcon.className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

                themeToggle.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-bs-theme');
                    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                    document.documentElement.setAttribute('data-bs-theme', newTheme);
                    localStorage.setItem('theme', newTheme);

                    // Update icon
                    themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

                    // Add smooth transition effect
                    document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
                    setTimeout(() => {
                        document.body.style.transition = '';
                    }, 300);
                });
            }

            // Modern scroll effects
            let ticking = false;

            function updateScrollEffects() {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;

                // Parallax effect for hero sections
                const heroElements = document.querySelectorAll('.hero-parallax');
                heroElements.forEach(element => {
                    element.style.transform = `translateY(${rate}px)`;
                });

                // Update reading progress
                const article = document.querySelector('.article-content');
                if (article) {
                    const articleTop = article.offsetTop;
                    const articleHeight = article.offsetHeight;
                    const windowHeight = window.innerHeight;
                    const scrollTop = window.pageYOffset;

                    const progress = Math.min(
                        Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
                        1
                    );

                    const progressBar = document.getElementById('reading-progress');
                    if (progressBar) {
                        progressBar.style.width = `${progress * 100}%`;
                    }
                }

                ticking = false;
            }

            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollEffects);
                    ticking = true;
                }
            }

            window.addEventListener('scroll', requestTick);

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            // Observe elements for animation
            document.querySelectorAll('.modern-card, .article-card').forEach(el => {
                observer.observe(el);
            });
        });
    </script>

    <!-- Add modern CSS for footer social links -->
    <style>
        .social-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-radius: var(--radius-lg);
            text-decoration: none;
            transition: all var(--transition-fast);
            backdrop-filter: blur(10px);
        }

        .social-link:hover {
            background: var(--primary-600);
            color: white;
            transform: translateY(-2px);
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: var(--neutral-400);
            text-decoration: none;
            transition: color var(--transition-fast);
            font-size: 0.9rem;
        }

        .footer-links a:hover {
            color: white;
            padding-right: 0.5rem;
        }

        .newsletter-form .modern-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .newsletter-form .modern-input:focus {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--primary-400);
        }
    </style>
</body>
</html>