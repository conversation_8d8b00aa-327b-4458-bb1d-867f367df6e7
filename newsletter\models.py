from django.db import models
from django.contrib.auth import get_user_model
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags

User = get_user_model()

class Subscriber(models.Model):
    """نموذج المشتركين في النشرة البريدية"""
    
    email = models.EmailField(unique=True, verbose_name='البريد الإلكتروني')
    name = models.CharField(max_length=100, blank=True, verbose_name='الاسم')
    is_active = models.BooleanField(default=True, verbose_name='نشط')
    subscribed_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الاشتراك')
    unsubscribed_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ إلغاء الاشتراك')
    
    # تفضيلات الاشتراك
    daily_digest = models.BooleanField(default=True, verbose_name='الملخص اليومي')
    weekly_digest = models.BooleanField(default=True, verbose_name='الملخص الأسبوعي')
    breaking_news = models.BooleanField(default=True, verbose_name='الأخبار العاجلة')
    
    class Meta:
        verbose_name = 'مشترك'
        verbose_name_plural = 'المشتركون'
        ordering = ['-subscribed_at']
    
    def __str__(self):
        return self.email
    
    def unsubscribe(self):
        """إلغاء الاشتراك"""
        from django.utils import timezone
        self.is_active = False
        self.unsubscribed_at = timezone.now()
        self.save()

class Newsletter(models.Model):
    """نموذج النشرات البريدية"""
    
    TYPE_CHOICES = [
        ('daily', 'يومية'),
        ('weekly', 'أسبوعية'),
        ('breaking', 'أخبار عاجلة'),
        ('custom', 'مخصصة'),
    ]
    
    STATUS_CHOICES = [
        ('draft', 'مسودة'),
        ('scheduled', 'مجدولة'),
        ('sent', 'مُرسلة'),
        ('failed', 'فشلت'),
    ]
    
    title = models.CharField(max_length=200, verbose_name='العنوان')
    content = models.TextField(verbose_name='المحتوى')
    newsletter_type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='custom', verbose_name='نوع النشرة')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name='الحالة')
    
    # الإرسال
    scheduled_at = models.DateTimeField(null=True, blank=True, verbose_name='موعد الإرسال')
    sent_at = models.DateTimeField(null=True, blank=True, verbose_name='تاريخ الإرسال')
    recipients_count = models.PositiveIntegerField(default=0, verbose_name='عدد المستلمين')
    
    # التواريخ
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')
    
    class Meta:
        verbose_name = 'نشرة بريدية'
        verbose_name_plural = 'النشرات البريدية'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def send_newsletter(self):
        """إرسال النشرة البريدية"""
        from django.utils import timezone
        
        # الحصول على المشتركين النشطين
        subscribers = Subscriber.objects.filter(is_active=True)
        
        # تصفية المشتركين حسب نوع النشرة
        if self.newsletter_type == 'daily':
            subscribers = subscribers.filter(daily_digest=True)
        elif self.newsletter_type == 'weekly':
            subscribers = subscribers.filter(weekly_digest=True)
        elif self.newsletter_type == 'breaking':
            subscribers = subscribers.filter(breaking_news=True)
        
        sent_count = 0
        for subscriber in subscribers:
            try:
                # إنشاء محتوى البريد الإلكتروني
                html_message = render_to_string('newsletter/email_template.html', {
                    'newsletter': self,
                    'subscriber': subscriber,
                })
                plain_message = strip_tags(html_message)
                
                # إرسال البريد الإلكتروني
                send_mail(
                    subject=self.title,
                    message=plain_message,
                    from_email='<EMAIL>',
                    recipient_list=[subscriber.email],
                    html_message=html_message,
                    fail_silently=False,
                )
                sent_count += 1
                
            except Exception as e:
                print(f"خطأ في إرسال البريد إلى {subscriber.email}: {e}")
        
        # تحديث حالة النشرة
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.recipients_count = sent_count
        self.save()
        
        return sent_count

class NewsletterTemplate(models.Model):
    """نموذج قوالب النشرات البريدية"""
    
    name = models.CharField(max_length=100, verbose_name='اسم القالب')
    subject_template = models.CharField(max_length=200, verbose_name='قالب العنوان')
    html_template = models.TextField(verbose_name='قالب HTML')
    is_default = models.BooleanField(default=False, verbose_name='القالب الافتراضي')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    
    class Meta:
        verbose_name = 'قالب نشرة'
        verbose_name_plural = 'قوالب النشرات'
    
    def __str__(self):
        return self.name
