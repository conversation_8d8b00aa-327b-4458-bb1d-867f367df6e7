from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.contrib import messages
from django.urls import reverse

from .models import Comment, CommentLike
from .forms import CommentForm

@login_required
@require_POST
def toggle_comment_like(request, comment_id):
    """تبديل الإعجاب بالتعليق"""
    try:
        comment = get_object_or_404(Comment, id=comment_id, status='approved')
        like, created = CommentLike.objects.get_or_create(
            comment=comment,
            user=request.user
        )
        
        if not created:
            like.delete()
            liked = False
        else:
            liked = True
        
        # تحديث عدد الإعجابات
        likes_count = comment.likes.count()
        comment.likes_count = likes_count
        comment.save(update_fields=['likes_count'])
        
        return JsonResponse({
            'success': True,
            'liked': liked,
            'likes_count': likes_count
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def reply_to_comment(request, comment_id):
    """الرد على تعليق"""
    try:
        parent_comment = get_object_or_404(Comment, id=comment_id, status='approved')
        
        form = CommentForm(request.POST)
        if form.is_valid():
            reply = form.save(commit=False)
            reply.article = parent_comment.article
            reply.author = request.user
            reply.parent = parent_comment
            reply.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم إرسال الرد بنجاح! سيتم مراجعته قبل النشر.'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'يرجى التحقق من البيانات المدخلة'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
def edit_comment(request, comment_id):
    """تعديل التعليق"""
    comment = get_object_or_404(Comment, id=comment_id, author=request.user)
    
    if request.method == 'POST':
        form = CommentForm(request.POST, instance=comment)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.status = 'pending'  # إعادة المراجعة بعد التعديل
            comment.save()
            messages.success(request, 'تم تحديث التعليق بنجاح! سيتم مراجعته قبل النشر.')
            return redirect('articles:article_detail', slug=comment.article.slug)
    else:
        form = CommentForm(instance=comment)
    
    context = {
        'form': form,
        'comment': comment,
    }
    return render(request, 'comments/edit_comment.html', context)

@login_required
def delete_comment(request, comment_id):
    """حذف التعليق"""
    comment = get_object_or_404(Comment, id=comment_id, author=request.user)
    
    if request.method == 'POST':
        article_slug = comment.article.slug
        comment.delete()
        messages.success(request, 'تم حذف التعليق بنجاح!')
        return redirect('articles:article_detail', slug=article_slug)
    
    context = {
        'comment': comment,
    }
    return render(request, 'comments/delete_comment.html', context)
