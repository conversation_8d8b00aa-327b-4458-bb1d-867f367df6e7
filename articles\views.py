from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.db.models import Q, Count, F
from django.http import JsonResponse, Http404
from django.core.paginator import Paginator
from django.contrib import messages
from django.urls import reverse_lazy
from django.utils import timezone
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

from .models import Article, Category, Tag, ArticleLike, ArticleView
from .forms import ArticleForm, CommentForm
from comments.models import Comment

class HomeView(ListView):
    """الصفحة الرئيسية"""
    model = Article
    template_name = 'articles/home.html'
    context_object_name = 'articles'
    paginate_by = 12
    
    def get_queryset(self):
        return Article.objects.filter(
            status='published',
            published_at__lte=timezone.now()
        ).select_related('author', 'category').prefetch_related('tags')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # المقالات المميزة
        context['featured_articles'] = Article.objects.filter(
            status='published',
            is_featured=True,
            published_at__lte=timezone.now()
        ).select_related('author', 'category')[:3]
        
        # الأخبار العاجلة
        context['breaking_news'] = Article.objects.filter(
            status='published',
            is_breaking_news=True,
            published_at__lte=timezone.now()
        ).select_related('author', 'category')[:5]
        
        # المقالات الأكثر قراءة
        context['popular_articles'] = Article.objects.filter(
            status='published',
            published_at__lte=timezone.now()
        ).order_by('-views_count')[:5]
        
        # الفئات
        context['categories'] = Category.objects.filter(is_active=True)
        
        # آخر التعليقات
        context['recent_comments'] = Comment.objects.filter(
            status='approved'
        ).select_related('author', 'article')[:5]
        
        return context

class ArticleDetailView(DetailView):
    """تفاصيل المقال"""
    model = Article
    template_name = 'articles/article_detail.html'
    context_object_name = 'article'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'
    
    def get_queryset(self):
        return Article.objects.filter(
            status='published',
            published_at__lte=timezone.now()
        ).select_related('author', 'category').prefetch_related('tags')
    
    def get_object(self, queryset=None):
        article = super().get_object(queryset)
        
        # تسجيل المشاهدة
        self.record_view(article)
        
        return article
    
    def record_view(self, article):
        """تسجيل مشاهدة المقال"""
        ip_address = self.get_client_ip()
        user_agent = self.request.META.get('HTTP_USER_AGENT', '')
        
        # تحقق من عدم تسجيل نفس المستخدم/IP أكثر من مرة في اليوم
        today = timezone.now().date()
        existing_view = ArticleView.objects.filter(
            article=article,
            ip_address=ip_address,
            created_at__date=today
        )
        
        if self.request.user.is_authenticated:
            existing_view = existing_view.filter(user=self.request.user)
        
        if not existing_view.exists():
            ArticleView.objects.create(
                article=article,
                user=self.request.user if self.request.user.is_authenticated else None,
                ip_address=ip_address,
                user_agent=user_agent
            )
            article.increment_views()
    
    def get_client_ip(self):
        """الحصول على IP العميل"""
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = self.request.META.get('REMOTE_ADDR')
        return ip
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        article = self.object
        
        # التعليقات المعتمدة
        context['comments'] = Comment.objects.filter(
            article=article,
            status='approved',
            parent=None
        ).select_related('author').prefetch_related('replies')
        
        # نموذج التعليق
        context['comment_form'] = CommentForm()
        
        # المقالات ذات الصلة
        context['related_articles'] = Article.objects.filter(
            category=article.category,
            status='published',
            published_at__lte=timezone.now()
        ).exclude(id=article.id)[:4]
        
        # تحقق من إعجاب المستخدم
        if self.request.user.is_authenticated:
            context['user_liked'] = ArticleLike.objects.filter(
                article=article,
                user=self.request.user
            ).exists()
        
        return context

def category_detail(request, slug):
    """مقالات الفئة"""
    category = get_object_or_404(Category, slug=slug)
    articles = Article.objects.filter(
        category=category,
        status='published',
        published_at__lte=timezone.now()
    ).select_related('author', 'category').prefetch_related('tags')
    
    paginator = Paginator(articles, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    categories = Category.objects.filter(is_active=True)
    
    context = {
        'category': category,
        'articles': page_obj,
        'categories': categories,
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages(),
    }
    
    return render(request, 'articles/category_detail.html', context)

class SearchView(ListView):
    """البحث في المقالات"""
    model = Article
    template_name = 'articles/search_results.html'
    context_object_name = 'articles'
    paginate_by = 12
    
    def get_queryset(self):
        query = self.request.GET.get('q', '').strip()
        if not query:
            return Article.objects.none()
        
        return Article.objects.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(excerpt__icontains=query) |
            Q(tags__name__icontains=query),
            status='published',
            published_at__lte=timezone.now()
        ).distinct().select_related('author', 'category').prefetch_related('tags')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['query'] = self.request.GET.get('q', '')
        context['categories'] = Category.objects.filter(is_active=True)
        return context

class LatestArticlesView(ListView):
    """آخر المقالات"""
    model = Article
    template_name = 'articles/latest.html'
    context_object_name = 'articles'
    paginate_by = 15
    
    def get_queryset(self):
        return Article.objects.filter(
            status='published',
            published_at__lte=timezone.now()
        ).select_related('author', 'category').prefetch_related('tags')

class FeaturedArticlesView(ListView):
    """المقالات المميزة"""
    model = Article
    template_name = 'articles/featured.html'
    context_object_name = 'articles'
    paginate_by = 12
    
    def get_queryset(self):
        return Article.objects.filter(
            status='published',
            is_featured=True,
            published_at__lte=timezone.now()
        ).select_related('author', 'category').prefetch_related('tags')

class ArticleCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """إنشاء مقال جديد"""
    model = Article
    form_class = ArticleForm
    template_name = 'articles/article_form.html'
    
    def test_func(self):
        return self.request.user.can_publish
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        if form.instance.status == 'published':
            form.instance.published_at = timezone.now()
        messages.success(self.request, 'تم إنشاء المقال بنجاح!')
        return super().form_valid(form)

class ArticleUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """تحديث المقال"""
    model = Article
    form_class = ArticleForm
    template_name = 'articles/article_form.html'
    
    def test_func(self):
        article = self.get_object()
        return (self.request.user == article.author or 
                self.request.user.can_moderate)
    
    def form_valid(self, form):
        if (form.instance.status == 'published' and 
            not form.instance.published_at):
            form.instance.published_at = timezone.now()
        messages.success(self.request, 'تم تحديث المقال بنجاح!')
        return super().form_valid(form)

class ArticleDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """حذف المقال"""
    model = Article
    template_name = 'articles/article_confirm_delete.html'
    success_url = reverse_lazy('articles:home')
    
    def test_func(self):
        article = self.get_object()
        return (self.request.user == article.author or 
                self.request.user.can_moderate)
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, 'تم حذف المقال بنجاح!')
        return super().delete(request, *args, **kwargs)

@login_required
@require_POST
def toggle_like(request, article_id):
    """تبديل الإعجاب بالمقال"""
    try:
        article = get_object_or_404(Article, id=article_id, status='published')
        like, created = ArticleLike.objects.get_or_create(
            article=article,
            user=request.user
        )
        
        if not created:
            like.delete()
            liked = False
        else:
            liked = True
        
        # تحديث عدد الإعجابات
        likes_count = article.likes.count()
        article.likes_count = likes_count
        article.save(update_fields=['likes_count'])
        
        return JsonResponse({
            'success': True,
            'liked': liked,
            'likes_count': likes_count
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@login_required
@require_POST
def add_comment(request, article_id):
    """إضافة تعليق"""
    try:
        article = get_object_or_404(Article, id=article_id, status='published')
        
        if not article.allow_comments:
            return JsonResponse({
                'success': False,
                'message': 'التعليقات غير مسموحة على هذا المقال'
            })
        
        form = CommentForm(request.POST)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.article = article
            comment.author = request.user
            
            # التحقق من التعليق الأصلي للرد
            parent_id = request.POST.get('parent_id')
            if parent_id:
                parent_comment = get_object_or_404(Comment, id=parent_id)
                comment.parent = parent_comment
            
            comment.save()
            
            return JsonResponse({
                'success': True,
                'message': 'تم إرسال التعليق بنجاح! سيتم مراجعته قبل النشر.'
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'يرجى التحقق من البيانات المدخلة'
            })
            
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def tag_detail(request, slug):
    """مقالات العلامة"""
    tag = get_object_or_404(Tag, slug=slug)
    articles = Article.objects.filter(
        tags=tag,
        status='published',
        published_at__lte=timezone.now()
    ).select_related('author', 'category').prefetch_related('tags')
    
    paginator = Paginator(articles, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # العلامات ذات الصلة (العلامات التي تظهر مع هذه العلامة في نفس المقالات)
    related_tags = Tag.objects.filter(
        articles__in=articles
    ).exclude(id=tag.id).annotate(
        usage_count=Count('articles')
    ).order_by('-usage_count')[:10]
    
    # العلامات الأكثر شعبية
    popular_tags = Tag.objects.annotate(
        article_count=Count('articles', filter=Q(articles__status='published'))
    ).filter(article_count__gt=0).order_by('-article_count')[:15]
    
    context = {
        'tag': tag,
        'articles': page_obj,
        'categories': Category.objects.filter(is_active=True).annotate(
            article_count=Count('articles', filter=Q(articles__status='published'))
        ),
        'related_tags': related_tags,
        'popular_tags': popular_tags,
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }
    
    return render(request, 'articles/tag_detail.html', context)

def author_articles(request, author_id):
    """مقالات الكاتب"""
    from django.contrib.auth import get_user_model
    User = get_user_model()
    
    author = get_object_or_404(User, id=author_id)
    articles = Article.objects.filter(
        author=author,
        status='published',
        published_at__lte=timezone.now()
    ).select_related('author', 'category').prefetch_related('tags')
    
    paginator = Paginator(articles, 12)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'author': author,
        'articles': page_obj,
        'categories': Category.objects.filter(is_active=True),
    }
    
    return render(request, 'articles/author_articles.html', context)
