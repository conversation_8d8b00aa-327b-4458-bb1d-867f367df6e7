/* Enhanced Navigation Styles */

/* Top Bar Enhancements */
.top-bar-enhanced {
  position: relative;
  overflow: hidden;
}

.top-bar-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shimmer 3s infinite;
}

/* Live DateTime */
.live-datetime {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Weather Widget */
.weather-widget {
  padding: 0.25rem 0.75rem;
  background: rgba(255,255,255,0.1);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

/* Enhanced Logo */
.logo-enhanced .logo-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.1); opacity: 0.1; }
}

/* Enhanced Search */
.search-enhanced .search-input-enhanced {
  background: white;
  border: 2px solid var(--neutral-300);
  transition: all var(--transition-fast);
}

.search-enhanced .search-input-enhanced:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.search-suggestions {
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-lg);
  background: white;
}

.search-suggestions.show {
  display: block !important;
  animation: slideInDown 0.3s ease-out;
}

/* Voice Search Button */
.voice-search-btn:hover {
  color: var(--primary-600) !important;
  transform: scale(1.1);
}

.voice-search-btn.recording {
  color: var(--accent-red) !important;
  animation: pulse 1s infinite;
}

/* Enhanced User Menu */
.user-menu-enhanced .dropdown-menu {
  min-width: 280px;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: none;
  padding: 0;
  overflow: hidden;
}

.user-menu-enhanced .dropdown-header {
  background: var(--gradient-primary);
  color: white;
  border: none;
  margin: 0;
}

.user-menu-enhanced .dropdown-item {
  padding: 0.75rem 1rem;
  transition: all var(--transition-fast);
  border: none;
}

.user-menu-enhanced .dropdown-item:hover {
  background: var(--primary-50);
  transform: translateX(5px);
}

/* Enhanced Navigation Menu */
.navigation-menu-enhanced {
  position: relative;
  z-index: 1000;
}

.nav-link-enhanced {
  transition: all var(--transition-fast);
  border-radius: var(--radius-md);
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-link-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.1);
  transition: left var(--transition-fast);
}

.nav-link-enhanced:hover::before {
  left: 0;
}

.nav-link-enhanced:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  width: 30px;
  height: 30px;
  position: relative;
}

.hamburger-icon {
  width: 100%;
  height: 100%;
  position: relative;
  transform: rotate(0deg);
  transition: 0.5s ease-in-out;
  cursor: pointer;
}

.hamburger-icon span {
  display: block;
  position: absolute;
  height: 3px;
  width: 100%;
  background: white;
  border-radius: 9px;
  opacity: 1;
  left: 0;
  transform: rotate(0deg);
  transition: 0.25s ease-in-out;
}

.hamburger-icon span:nth-child(1) {
  top: 0px;
}

.hamburger-icon span:nth-child(2) {
  top: 10px;
}

.hamburger-icon span:nth-child(3) {
  top: 20px;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-icon span:nth-child(1) {
  top: 10px;
  transform: rotate(135deg);
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-icon span:nth-child(2) {
  opacity: 0;
  left: -60px;
}

.mobile-menu-toggle[aria-expanded="true"] .hamburger-icon span:nth-child(3) {
  top: 10px;
  transform: rotate(-135deg);
}

/* Breaking News Ticker */
.breaking-news-ticker-enhanced {
  background: rgba(255,255,255,0.1);
  border-radius: var(--radius-lg);
  padding: 0.5rem 1rem;
  backdrop-filter: blur(10px);
}

.ticker-scroll {
  animation: scroll-left 30s linear infinite;
  white-space: nowrap;
}

@keyframes scroll-left {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.ticker-item {
  transition: color var(--transition-fast);
}

.ticker-item:hover {
  color: var(--primary-200) !important;
}

/* Mobile Ticker */
.mobile-ticker {
  overflow: hidden;
  white-space: nowrap;
}

.mobile-ticker-item {
  display: inline-block;
  animation: scroll-mobile 20s linear infinite;
}

@keyframes scroll-mobile {
  0% { transform: translateX(100vw); }
  100% { transform: translateX(-100%); }
}

/* Sticky Navigation */
.modern-navigation-enhanced.sticky {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1030;
  animation: slideInDown 0.3s ease-out;
}

.modern-navigation-enhanced.sticky .top-bar-enhanced {
  display: none;
}

.modern-navigation-enhanced.sticky .main-header-enhanced {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
}

/* Notifications Dropdown */
.notifications-dropdown .dropdown-menu {
  border-radius: var(--radius-xl);
  border: none;
  box-shadow: var(--shadow-xl);
  padding: 0;
  overflow: hidden;
}

.notifications-dropdown .dropdown-item {
  border-bottom: 1px solid var(--neutral-100);
  transition: all var(--transition-fast);
}

.notifications-dropdown .dropdown-item:hover {
  background: var(--primary-50);
  transform: translateX(5px);
}

.notifications-dropdown .dropdown-item:last-child {
  border-bottom: none;
}

/* Language Switcher */
.language-switcher button:hover {
  background: rgba(255,255,255,0.1);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 992px) {
  .search-enhanced {
    margin: 1rem 0;
  }
  
  .user-menu-enhanced,
  .auth-buttons {
    justify-content: center;
    margin-top: 1rem;
  }
  
  .nav-link-enhanced {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: var(--radius-md);
  }
  
  .mobile-nav-content {
    background: rgba(255,255,255,0.1);
    border-radius: var(--radius-lg);
    margin-top: 1rem;
    backdrop-filter: blur(10px);
  }
}

@media (max-width: 768px) {
  .logo-enhanced h1 {
    font-size: 1.5rem !important;
  }
  
  .logo-enhanced .logo-icon {
    width: 50px !important;
    height: 50px !important;
  }
  
  .search-input-enhanced {
    font-size: 0.9rem !important;
    padding: 0.6rem 3rem 0.6rem 0.8rem !important;
  }
  
  .weather-widget,
  .language-switcher {
    display: none !important;
  }
}

/* Dark Theme Adjustments */
[data-bs-theme="dark"] .main-header-enhanced {
  background: rgba(17, 24, 39, 0.95) !important;
  border-bottom-color: var(--neutral-200);
}

[data-bs-theme="dark"] .search-input-enhanced {
  background: var(--neutral-100);
  border-color: var(--neutral-200);
  color: var(--neutral-800);
}

[data-bs-theme="dark"] .search-suggestions {
  background: var(--neutral-100);
}

[data-bs-theme="dark"] .user-menu-enhanced .dropdown-menu {
  background: var(--neutral-100);
}

[data-bs-theme="dark"] .notifications-dropdown .dropdown-menu {
  background: var(--neutral-100);
}

/* Print Styles */
@media print {
  .modern-navigation-enhanced {
    display: none !important;
  }
  
  .breaking-news-mobile-enhanced {
    display: none !important;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .ticker-scroll,
  .mobile-ticker-item,
  .logo-pulse {
    animation: none !important;
  }
  
  .nav-link-enhanced,
  .dropdown-item {
    transition: none !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .nav-link-enhanced {
    border: 1px solid white;
  }
  
  .search-input-enhanced {
    border: 2px solid var(--neutral-900);
  }
  
  .modern-card {
    border: 2px solid var(--neutral-900);
  }
}
