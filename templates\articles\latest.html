{% extends 'base/base.html' %}
{% load static %}

{% block title %}آخر الأخبار - تاتا نيوز{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="section-title">
                    <i class="fas fa-clock text-primary"></i>
                    آخر الأخبار
                </h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'articles:home' %}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">آخر الأخبار</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Articles -->
        <div class="col-lg-8">
            <div class="row">
                {% for article in articles %}
                <div class="col-md-6 mb-4">
                    <div class="card article-card h-100">
                        {% if article.featured_image %}
                            <div class="position-relative">
                                <img src="{{ article.featured_image.url }}" class="card-img-top" alt="{{ article.image_alt|default:article.title }}">
                                <span class="badge position-absolute top-0 end-0 m-2" style="background-color: {{ article.category.color }};">
                                    {{ article.category.name }}
                                </span>
                                {% if article.is_breaking_news %}
                                    <span class="badge bg-danger position-absolute top-0 start-0 m-2">عاجل</span>
                                {% endif %}
                                {% if article.is_featured %}
                                    <span class="badge bg-warning text-dark position-absolute" style="top: 40px; start: 0; margin: 8px;">مميز</span>
                                {% endif %}
                            </div>
                        {% endif %}
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">
                                <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                    {{ article.title }}
                                </a>
                            </h5>
                            <p class="card-text text-muted flex-grow-1">{{ article.excerpt|truncatewords:20 }}</p>
                            <div class="article-meta small text-muted mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                        <br>
                                        <i class="fas fa-calendar"></i> {{ article.published_at|date:"d M Y - H:i" }}
                                    </div>
                                    <div class="text-end">
                                        <i class="fas fa-eye"></i> {{ article.views_count }}
                                        <br>
                                        <i class="fas fa-heart"></i> {{ article.likes_count }}
                                    </div>
                                </div>
                            </div>
                            {% if article.tags.exists %}
                            <div class="mt-2">
                                {% for tag in article.tags.all|slice:":3" %}
                                    <a href="{% url 'articles:tag_detail' tag.slug %}" class="tag">#{{ tag.name }}</a>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <h4>لا توجد مقالات حالياً</h4>
                        <p>لم يتم نشر أي مقالات بعد. تابعنا للحصول على آخر الأخبار.</p>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="تنقل الصفحات" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1">الأولى</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Categories -->
            <div class="sidebar">
                <h5>الأقسام</h5>
                <div class="row">
                    {% for category in categories %}
                    <div class="col-6 mb-2">
                        <a href="{{ category.get_absolute_url }}" class="btn btn-outline-secondary btn-sm w-100">
                            {% if category.icon %}
                                <i class="{{ category.icon }}"></i>
                            {% endif %}
                            {{ category.name }}
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Newsletter -->
            <div class="newsletter-section">
                <h3>اشترك في نشرتنا البريدية</h3>
                <p>احصل على آخر الأخبار والمقالات مباشرة في بريدك الإلكتروني</p>
                <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                    {% csrf_token %}
                    <div class="input-group mb-3">
                        <input type="email" class="form-control" name="email" placeholder="بريدك الإلكتروني" required>
                        <button class="btn btn-light" type="submit">اشتراك</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}