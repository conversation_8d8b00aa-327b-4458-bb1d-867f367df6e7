# تاتا نيوز - جريدة إلكترونية شاملة

## نظرة عامة

تاتا نيوز هي جريدة إلكترونية شاملة مبنية باستخدام Django، تدعم اللغة العربية بالكامل مع دعم RTL. تتضمن نظام إدارة المقالات، التعليقات، المستخدمين والصلاحيات، البحث المتقدم، والنشرة البريدية.

## المميزات الرئيسية

### 🗞️ نظام إدارة المقالات
- إنشاء وتحرير المقالات مع محرر نصوص متقدم (CKEditor)
- تصنيف المقالات حسب الفئات والعلامات
- نظام الأولويات والمقالات المميزة
- الأخبار العاجلة مع شريط متحرك
- نظام المشاهدات والإعجابات
- جدولة النشر

### 👥 نظام المستخدمين والصلاحيات
- أدوار متعددة: مدير، محرر، كاتب، قارئ
- ملفات شخصية مخصصة
- نظام التحقق من البريد الإلكتروني
- لوحة تحكم شخصية لكل مستخدم

### 💬 نظام التعليقات
- تعليقات متداخلة (ردود)
- نظام الموافقة على التعليقات
- إعجابات التعليقات
- فلترة المحتوى غير المناسب

### 🔍 البحث المتقدم
- البحث في العناوين والمحتوى
- فلترة حسب الفئة والتاريخ
- تمييز نتائج البحث
- اقتراحات البحث

### 📧 النشرة البريدية
- اشتراك وإلغاء اشتراك
- تفضيلات الاشتراك (يومي، أسبوعي، عاجل)
- قوالب النشرات المخصصة
- جدولة الإرسال

### 🎨 التصميم والواجهة
- دعم كامل للعربية مع RTL
- تصميم متجاوب (Responsive)
- وضع داكن تلقائي
- أيقونات Font Awesome
- خطوط عربية جميلة

## التقنيات المستخدمة

- **Backend**: Django 5.1
- **Database**: SQLite (قابل للتغيير إلى PostgreSQL)
- **Frontend**: Bootstrap 5 RTL
- **JavaScript**: Vanilla JS
- **Icons**: Font Awesome 6
- **Editor**: CKEditor 4
- **Authentication**: Django Allauth

## متطلبات التشغيل

- Python 3.8+
- Django 5.1
- المكتبات المذكورة في `requirements.txt`

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd tatanews
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء البيانات التجريبية
```bash
python create_sample_data.py
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

## بيانات تسجيل الدخول الافتراضية

بعد تشغيل سكريبت البيانات التجريبية:

- **المدير**: `admin` / `admin123`
- **المحرر**: `editor` / `editor123`
- **الكاتب**: `author1` / `author123`

## هيكل المشروع

```
tatanews/
├── accounts/           # تطبيق المستخدمين
├── articles/           # تطبيق المقالات
├── comments/           # تطبيق التعليقات
├── newsletter/         # تطبيق النشرة البريدية
├── static/            # الملفات الثابتة
│   ├── css/
│   ├── js/
│   └── images/
├── templates/         # القوالب
│   ├── base/
│   ├── articles/
│   ├── accounts/
│   ├── comments/
│   └── newsletter/
├── media/             # ملفات الوسائط المرفوعة
└── tatanews/          # إعدادات المشروع
```

## الميزات المتقدمة

### نظام الصلاحيات
- **مدير**: صلاحيات كاملة
- **محرر**: إدارة المقالات والتعليقات
- **كاتب**: إنشاء وتحرير مقالاته فقط
- **قارئ**: قراءة وتعليق

### نظام الإشعارات
- إشعارات المتصفح للأخبار العاجلة
- رسائل تأكيد العمليات
- تنبيهات الأخطاء

### تحسين محركات البحث (SEO)
- Meta tags مخصصة
- Open Graph tags
- Twitter Cards
- Structured Data (JSON-LD)
- URLs صديقة لمحركات البحث

### الأداء
- تحميل الصور البطيء (Lazy Loading)
- ضغط الملفات الثابتة
- فهرسة قاعدة البيانات
- تخزين مؤقت للاستعلامات

## التخصيص

### إضافة فئات جديدة
```python
# في Django Admin أو برمجياً
category = Category.objects.create(
    name="فئة جديدة",
    color="#ff5722",
    icon="fas fa-newspaper"
)
```

### تخصيص القوالب
القوالب موجودة في مجلد `templates/` ويمكن تعديلها حسب الحاجة.

### إضافة أنماط CSS
```css
/* في static/css/style.css */
.custom-style {
    /* أنماطك المخصصة */
}
```

## النشر في الإنتاج

### 1. إعدادات الإنتاج
```python
# في settings.py
DEBUG = False
ALLOWED_HOSTS = ['yourdomain.com']

# قاعدة بيانات PostgreSQL
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'tatanews_db',
        'USER': 'your_user',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
    }
}
```

### 2. جمع الملفات الثابتة
```bash
python manage.py collectstatic
```

### 3. إعداد خادم الويب
يُنصح باستخدام Nginx + Gunicorn أو Apache + mod_wsgi.

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم:
- إنشاء Issue في GitHub
- مراسلة الفريق على: <EMAIL>

## خارطة الطريق

### الإصدار القادم (v2.0)
- [ ] تطبيق جوال
- [ ] نظام الإشعارات المتقدم
- [ ] تحليلات مفصلة
- [ ] نظام الاشتراكات المدفوعة
- [ ] API RESTful
- [ ] دعم متعدد اللغات

### تحسينات مستقبلية
- [ ] نظام التعليقات المباشر
- [ ] محرر نصوص أكثر تقدماً
- [ ] نظام التصويت على المقالات
- [ ] تكامل مع وسائل التواصل الاجتماعي
- [ ] نظام البودكاست

## الشكر والتقدير

شكر خاص لـ:
- فريق Django لإطار العمل الرائع
- مجتمع Bootstrap للتصميم المتجاوب
- Font Awesome للأيقونات الجميلة
- جميع المساهمين في المشروع

---

**تاتا نيوز** - جريدتك الإلكترونية الشاملة 📰