from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Category, Tag, Article, ArticleLike, ArticleView

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'color_display', 'is_active', 'articles_count', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'description']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at']
    
    def color_display(self, obj):
        return format_html(
            '<span style="background-color: {}; padding: 5px 10px; border-radius: 3px; color: white;">{}</span>',
            obj.color,
            obj.color
        )
    color_display.short_description = 'اللون'
    
    def articles_count(self, obj):
        count = obj.articles.filter(status='published').count()
        url = reverse('admin:articles_article_changelist') + f'?category__id__exact={obj.id}'
        return format_html('<a href="{}">{} مقال</a>', url, count)
    articles_count.short_description = 'عدد المقالات'

@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ['name', 'slug', 'articles_count', 'created_at']
    search_fields = ['name']
    prepopulated_fields = {'slug': ('name',)}
    readonly_fields = ['created_at']
    
    def articles_count(self, obj):
        count = obj.articles.filter(status='published').count()
        return f'{count} مقال'
    articles_count.short_description = 'عدد المقالات'

@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    list_display = [
        'title', 'author', 'category', 'status', 'priority',
        'is_featured', 'is_breaking_news', 'views_count',
        'likes_count', 'published_at', 'created_at'
    ]
    list_filter = [
        'status', 'priority', 'category', 'is_featured',
        'is_breaking_news', 'allow_comments', 'created_at', 'published_at'
    ]
    search_fields = ['title', 'content', 'author__first_name', 'author__last_name']
    prepopulated_fields = {'slug': ('title',)}
    readonly_fields = [
        'views_count', 'likes_count', 'created_at', 'updated_at',
        'featured_image_preview'
    ]
    filter_horizontal = ['tags']
    date_hierarchy = 'published_at'
    
    fieldsets = (
        ('المعلومات الأساسية', {
            'fields': ('title', 'slug', 'subtitle', 'author', 'category')
        }),
        ('المحتوى', {
            'fields': ('content', 'excerpt', 'tags')
        }),
        ('الصورة المميزة', {
            'fields': ('featured_image', 'featured_image_preview', 'image_alt'),
            'classes': ('collapse',)
        }),
        ('الإعدادات', {
            'fields': (
                'status', 'priority', 'is_featured',
                'allow_comments', 'is_breaking_news', 'published_at'
            )
        }),
        ('الإحصائيات', {
            'fields': ('views_count', 'likes_count'),
            'classes': ('collapse',)
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def featured_image_preview(self, obj):
        if obj.featured_image:
            return mark_safe(f'<img src="{obj.featured_image.url}" style="max-height: 200px; max-width: 300px;">')
        return "لا توجد صورة"
    featured_image_preview.short_description = 'معاينة الصورة'
    
    def save_model(self, request, obj, form, change):
        if not change:  # إنشاء مقال جديد
            obj.author = request.user
        
        # تعيين تاريخ النشر إذا تم تغيير الحالة إلى منشور
        if obj.status == 'published' and not obj.published_at:
            from django.utils import timezone
            obj.published_at = timezone.now()
        
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if request.user.is_superuser:
            return qs
        return qs.filter(author=request.user)
    
    actions = ['make_published', 'make_draft', 'make_featured']
    
    def make_published(self, request, queryset):
        from django.utils import timezone
        updated = queryset.update(status='published', published_at=timezone.now())
        self.message_user(request, f'تم نشر {updated} مقال.')
    make_published.short_description = 'نشر المقالات المحددة'
    
    def make_draft(self, request, queryset):
        updated = queryset.update(status='draft')
        self.message_user(request, f'تم تحويل {updated} مقال إلى مسودة.')
    make_draft.short_description = 'تحويل إلى مسودة'
    
    def make_featured(self, request, queryset):
        updated = queryset.update(is_featured=True)
        self.message_user(request, f'تم تمييز {updated} مقال.')
    make_featured.short_description = 'تمييز المقالات'

@admin.register(ArticleLike)
class ArticleLikeAdmin(admin.ModelAdmin):
    list_display = ['article', 'user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['article__title', 'user__first_name', 'user__last_name']
    readonly_fields = ['created_at']

@admin.register(ArticleView)
class ArticleViewAdmin(admin.ModelAdmin):
    list_display = ['article', 'user', 'ip_address', 'created_at']
    list_filter = ['created_at']
    search_fields = ['article__title', 'user__first_name', 'user__last_name', 'ip_address']
    readonly_fields = ['created_at']
    
    def has_add_permission(self, request):
        return False  # منع إضافة مشاهدات يدوياً
    
    def has_change_permission(self, request, obj=None):
        return False  # منع تعديل المشاهدات
