// تاتا نيوز - JavaScript العصري

// Modern utilities and enhancements
class ModernUtils {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupSmoothScrolling();
        this.setupModernAnimations();
        this.setupPerformanceOptimizations();
    }

    // Lazy Loading with Intersection Observer
    setupLazyLoading() {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    
                    // Add loading skeleton
                    img.classList.add('loading-skeleton');
                    
                    // Load image
                    const tempImg = new Image();
                    tempImg.onload = () => {
                        img.src = img.dataset.src;
                        img.classList.remove('loading-skeleton');
                        img.classList.add('fade-in');
                        observer.unobserve(img);
                    };
                    tempImg.src = img.dataset.src;
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.01
        });

        // Observe all images with data-src
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Enhanced smooth scrolling
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                
                if (target) {
                    const headerHeight = document.querySelector('.modern-nav').offsetHeight;
                    const targetPosition = target.offsetTop - headerHeight - 20;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    // Modern animations and micro-interactions
    setupModernAnimations() {
        // Stagger animation for cards
        const cards = document.querySelectorAll('.modern-card, .article-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // Hover effects for interactive elements
        document.querySelectorAll('.hover-lift').forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.transform = 'translateY(-4px)';
                element.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'translateY(0)';
                element.style.boxShadow = '';
            });
        });

        // Scale effect for images
        document.querySelectorAll('.hover-scale').forEach(element => {
            element.addEventListener('mouseenter', () => {
                element.style.transform = 'scale(1.05)';
            });
            
            element.addEventListener('mouseleave', () => {
                element.style.transform = 'scale(1)';
            });
        });
    }

    // Performance optimizations
    setupPerformanceOptimizations() {
        // Debounced scroll handler
        let scrollTimeout;
        window.addEventListener('scroll', () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            
            scrollTimeout = setTimeout(() => {
                this.handleScroll();
            }, 10);
        });

        // Preload critical resources
        this.preloadCriticalResources();
    }

    handleScroll() {
        const scrolled = window.pageYOffset;
        
        // Update navigation background
        const nav = document.querySelector('.modern-nav');
        if (nav) {
            if (scrolled > 100) {
                nav.style.background = 'rgba(255, 255, 255, 0.98)';
                nav.style.backdropFilter = 'blur(20px)';
            } else {
                nav.style.background = 'rgba(255, 255, 255, 0.95)';
                nav.style.backdropFilter = 'blur(10px)';
            }
        }

        // Show/hide back to top button
        const backToTop = document.getElementById('backToTop');
        if (backToTop) {
            if (scrolled > 300) {
                backToTop.style.display = 'block';
                backToTop.style.opacity = '1';
            } else {
                backToTop.style.opacity = '0';
                setTimeout(() => {
                    if (window.pageYOffset <= 300) {
                        backToTop.style.display = 'none';
                    }
                }, 300);
            }
        }
    }

    preloadCriticalResources() {
        // Preload important images
        const criticalImages = [
            '/static/images/logo.png',
            '/static/images/hero-bg.jpg'
        ];

        criticalImages.forEach(src => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }
}

// Modern form enhancements
class ModernForms {
    constructor() {
        this.init();
    }

    init() {
        this.enhanceInputs();
        this.setupFormValidation();
        this.setupAsyncForms();
    }

    enhanceInputs() {
        // Add floating labels effect
        document.querySelectorAll('.modern-input').forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                if (!input.value) {
                    input.parentElement.classList.remove('focused');
                }
            });

            // Check if input has value on load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });
    }

    setupFormValidation() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                }
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], textarea[required]');

        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'هذا الحقل مطلوب');
                isValid = false;
            } else if (input.type === 'email' && !this.isValidEmail(input.value)) {
                this.showFieldError(input, 'يرجى إدخال بريد إلكتروني صحيح');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        return isValid;
    }

    showFieldError(input, message) {
        input.classList.add('is-invalid');
        
        let errorElement = input.parentElement.querySelector('.invalid-feedback');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            input.parentElement.appendChild(errorElement);
        }
        
        errorElement.textContent = message;
    }

    clearFieldError(input) {
        input.classList.remove('is-invalid');
        const errorElement = input.parentElement.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.remove();
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    setupAsyncForms() {
        // Handle newsletter subscription
        document.querySelectorAll('.newsletter-form').forEach(form => {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleNewsletterSubmission(form);
            });
        });
    }

    async handleNewsletterSubmission(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalContent = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاشتراك...';

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCsrfToken(),
                },
            });

            const data = await response.json();

            if (data.success) {
                this.showNotification('تم الاشتراك بنجاح في النشرة البريدية!', 'success');
                form.reset();
            } else {
                this.showNotification(data.message || 'حدث خطأ أثناء الاشتراك', 'error');
            }
        } catch (error) {
            this.showNotification('حدث خطأ في الاتصال', 'error');
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalContent;
        }
    }

    getCsrfToken() {
        const token = document.querySelector('[name=csrfmiddlewaretoken]');
        return token ? token.value : '';
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
        
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                const bsAlert = new bootstrap.Alert(notification);
                bsAlert.close();
            }
        }, 5000);
    }
}

// Initialize modern features
document.addEventListener('DOMContentLoaded', () => {
    new ModernUtils();
    new ModernForms();
});

// Modern Interactive Features
class ModernInteractions {
    constructor() {
        this.init();
    }

    init() {
        this.setupAdvancedScrollEffects();
        this.setupInteractiveElements();
        this.setupKeyboardNavigation();
        this.setupTouchGestures();
        this.setupAdvancedAnimations();
    }

    setupAdvancedScrollEffects() {
        // Parallax scrolling for hero elements
        const parallaxElements = document.querySelectorAll('[data-parallax]');

        if (parallaxElements.length > 0) {
            let ticking = false;

            const updateParallax = () => {
                const scrolled = window.pageYOffset;

                parallaxElements.forEach(element => {
                    const speed = element.dataset.parallax || 0.5;
                    const yPos = -(scrolled * speed);
                    element.style.transform = `translateY(${yPos}px)`;
                });

                ticking = false;
            };

            const requestParallaxUpdate = () => {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            };

            window.addEventListener('scroll', requestParallaxUpdate);
        }

        // Reveal animations on scroll
        const revealElements = document.querySelectorAll('[data-reveal]');

        if (revealElements.length > 0) {
            const revealObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const animation = element.dataset.reveal || 'fade-in';
                        const delay = element.dataset.delay || 0;

                        setTimeout(() => {
                            element.classList.add(animation);
                        }, delay);

                        revealObserver.unobserve(element);
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            revealElements.forEach(element => {
                revealObserver.observe(element);
            });
        }
    }

    setupInteractiveElements() {
        // Enhanced button interactions
        document.querySelectorAll('.btn-modern-primary, .btn-modern-secondary').forEach(button => {
            button.addEventListener('mouseenter', this.createRippleEffect);
            button.addEventListener('click', this.createClickEffect);
        });

        // Card hover effects
        document.querySelectorAll('.modern-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
                card.style.boxShadow = 'var(--shadow-xl)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = 'var(--shadow-sm)';
            });
        });

        // Interactive tooltips
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            this.createTooltip(element);
        });

        // Copy to clipboard functionality
        document.querySelectorAll('[data-copy]').forEach(element => {
            element.addEventListener('click', () => {
                const text = element.dataset.copy || element.textContent;
                this.copyToClipboard(text);
            });
        });
    }

    setupKeyboardNavigation() {
        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            // Escape key to close modals/dropdowns
            if (e.key === 'Escape') {
                document.querySelectorAll('.dropdown-menu.show').forEach(dropdown => {
                    bootstrap.Dropdown.getInstance(dropdown.previousElementSibling)?.hide();
                });
            }

            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input, input[name="q"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Focus management for accessibility
        document.querySelectorAll('.modern-card, .article-card').forEach(card => {
            card.setAttribute('tabindex', '0');

            card.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    const link = card.querySelector('a');
                    if (link) {
                        link.click();
                    }
                }
            });
        });
    }

    setupTouchGestures() {
        // Touch gestures for mobile
        let startX, startY, startTime;

        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
            startTime = Date.now();
        });

        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;

            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const endTime = Date.now();

            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;

            // Swipe detection
            if (Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // Swipe right
                    this.handleSwipeRight();
                } else {
                    // Swipe left
                    this.handleSwipeLeft();
                }
            }

            startX = startY = null;
        });
    }

    setupAdvancedAnimations() {
        // Staggered animations for lists
        document.querySelectorAll('[data-stagger]').forEach(container => {
            const items = container.children;
            const delay = parseInt(container.dataset.stagger) || 100;

            Array.from(items).forEach((item, index) => {
                item.style.animationDelay = `${index * delay}ms`;
            });
        });

        // Morphing animations
        document.querySelectorAll('[data-morph]').forEach(element => {
            element.addEventListener('click', () => {
                this.morphElement(element);
            });
        });
    }

    createRippleEffect(e) {
        const button = e.currentTarget;
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;

        button.style.position = 'relative';
        button.style.overflow = 'hidden';
        button.appendChild(ripple);

        setTimeout(() => ripple.remove(), 600);
    }

    createClickEffect(e) {
        const element = e.currentTarget;
        element.style.transform = 'scale(0.95)';

        setTimeout(() => {
            element.style.transform = '';
        }, 150);
    }

    createTooltip(element) {
        const tooltip = document.createElement('div');
        tooltip.className = 'modern-tooltip';
        tooltip.textContent = element.dataset.tooltip;
        tooltip.style.cssText = `
            position: absolute;
            background: var(--neutral-900);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
            pointer-events: none;
        `;

        document.body.appendChild(tooltip);

        element.addEventListener('mouseenter', () => {
            const rect = element.getBoundingClientRect();
            tooltip.style.left = `${rect.left + rect.width / 2}px`;
            tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;
            tooltip.style.transform = 'translateX(-50%)';
            tooltip.style.opacity = '1';
            tooltip.style.visibility = 'visible';
        });

        element.addEventListener('mouseleave', () => {
            tooltip.style.opacity = '0';
            tooltip.style.visibility = 'hidden';
        });
    }

    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('تم نسخ النص إلى الحافظة!', 'success');
        }).catch(() => {
            this.showNotification('فشل في نسخ النص', 'error');
        });
    }

    handleSwipeRight() {
        // Handle right swipe (e.g., go back)
        if (window.history.length > 1) {
            window.history.back();
        }
    }

    handleSwipeLeft() {
        // Handle left swipe (e.g., next article)
        const nextLink = document.querySelector('[data-next-article]');
        if (nextLink) {
            nextLink.click();
        }
    }

    morphElement(element) {
        const currentState = element.dataset.morphState || 'default';
        const newState = currentState === 'default' ? 'morphed' : 'default';

        element.style.transition = 'all 0.3s ease';
        element.dataset.morphState = newState;

        if (newState === 'morphed') {
            element.style.transform = 'scale(1.1) rotate(5deg)';
            element.style.borderRadius = 'var(--radius-2xl)';
        } else {
            element.style.transform = '';
            element.style.borderRadius = '';
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `modern-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: ${type === 'success' ? 'var(--accent-green)' : type === 'error' ? 'var(--accent-red)' : 'var(--primary-600)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
            z-index: 9999;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Animate out and remove
        setTimeout(() => {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// Initialize modern interactions
document.addEventListener('DOMContentLoaded', () => {
    new ModernInteractions();
});

// Export for use in other scripts
window.ModernUtils = ModernUtils;
window.ModernForms = ModernForms;
window.ModernInteractions = ModernInteractions;
