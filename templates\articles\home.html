{% extends 'base/base.html' %}
{% load static %}

{% block title %}تاتا نيوز - الصفحة الرئيسية{% endblock %}

{% block content %}
<div class="container-fluid px-0">
    <!-- Hero Section -->
    {% if featured_articles %}
    <section class="hero-section mb-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    {% for article in featured_articles|slice:":1" %}
                    <div class="card featured-article mb-4">
                        {% if article.featured_image %}
                            <img src="{{ article.featured_image.url }}" class="card-img" alt="{{ article.image_alt|default:article.title }}">
                        {% endif %}
                        <div class="card-img-overlay">
                            <span class="badge bg-danger category-badge">{{ article.category.name }}</span>
                            <h2 class="card-title">
                                <a href="{{ article.get_absolute_url }}" class="text-white text-decoration-none">
                                    {{ article.title }}
                                </a>
                            </h2>
                            <p class="card-text">{{ article.excerpt|truncatewords:30 }}</p>
                            <div class="article-meta">
                                <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                <i class="fas fa-calendar me-1"></i> {{ article.published_at|date:"d M Y" }}
                                <i class="fas fa-eye me-1"></i> {{ article.views_count }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="col-lg-4">
                    {% for article in featured_articles|slice:"1:3" %}
                    <div class="card article-card mb-3">
                        {% if article.featured_image %}
                            <img src="{{ article.featured_image.url }}" class="card-img-top" alt="{{ article.image_alt|default:article.title }}">
                        {% endif %}
                        <div class="card-body">
                            <span class="badge" style="background-color: {{ article.category.color }};">{{ article.category.name }}</span>
                            <h5 class="card-title mt-2">
                                <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                    {{ article.title|truncatewords:8 }}
                                </a>
                            </h5>
                            <p class="card-text text-muted small">{{ article.excerpt|truncatewords:15 }}</p>
                            <div class="article-meta small">
                                <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                <i class="fas fa-calendar me-1"></i> {{ article.published_at|date:"d M" }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </section>
    {% endif %}
    
    <!-- Main Content -->
    <div class="container">
        <div class="row">
            <!-- Articles Section -->
            <div class="col-lg-8">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h3 class="section-title">آخر الأخبار</h3>
                    <a href="{% url 'articles:latest' %}" class="btn btn-outline-primary">عرض المزيد</a>
                </div>
                
                <div class="row articles-container">
                    {% for article in articles %}
                    <div class="col-md-6 mb-4">
                        <div class="card article-card h-100">
                            {% if article.featured_image %}
                                <div class="position-relative">
                                    <img src="{{ article.featured_image.url }}" class="card-img-top" alt="{{ article.image_alt|default:article.title }}">
                                    <span class="badge position-absolute top-0 end-0 m-2" style="background-color: {{ article.category.color }};">
                                        {{ article.category.name }}
                                    </span>
                                    {% if article.is_breaking_news %}
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">عاجل</span>
                                    {% endif %}
                                </div>
                            {% endif %}
                            <div class="card-body d-flex flex-column">
                                <h5 class="card-title">
                                    <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                        {{ article.title }}
                                    </a>
                                </h5>
                                <p class="card-text text-muted flex-grow-1">{{ article.excerpt|truncatewords:20 }}</p>
                                <div class="article-meta small text-muted mt-auto">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <i class="fas fa-user"></i> {{ article.author.get_full_name }}
                                            <br>
                                            <i class="fas fa-calendar"></i> {{ article.published_at|date:"d M Y" }}
                                        </div>
                                        <div class="text-end">
                                            <i class="fas fa-eye"></i> {{ article.views_count }}
                                            <br>
                                            <i class="fas fa-heart"></i> {{ article.likes_count }}
                                        </div>
                                    </div>
                                </div>
                                {% if article.tags.exists %}
                                <div class="mt-2">
                                    {% for tag in article.tags.all|slice:":3" %}
                                        <a href="{% url 'articles:tag_detail' tag.slug %}" class="tag">#{{ tag.name }}</a>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">الأولى</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">السابقة</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ page_obj.number }} من {{ page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">التالية</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">الأخيرة</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Popular Articles -->
                <div class="sidebar">
                    <h5>الأكثر قراءة</h5>
                    {% for article in popular_articles %}
                    <div class="popular-article">
                        {% if article.featured_image %}
                            <img src="{{ article.featured_image.url }}" alt="{{ article.title }}">
                        {% endif %}
                        <div>
                            <h6>
                                <a href="{{ article.get_absolute_url }}" class="text-decoration-none">
                                    {{ article.title|truncatewords:8 }}
                                </a>
                            </h6>
                            <small class="text-muted">
                                <i class="fas fa-eye"></i> {{ article.views_count }} مشاهدة
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Categories -->
                <div class="sidebar">
                    <h5>الأقسام</h5>
                    <div class="row">
                        {% for category in categories %}
                        <div class="col-6 mb-2">
                            <a href="{{ category.get_absolute_url }}" class="btn btn-outline-secondary btn-sm w-100">
                                {% if category.icon %}
                                    <i class="{{ category.icon }}"></i>
                                {% endif %}
                                {{ category.name }}
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <!-- Newsletter Subscription -->
                <div class="newsletter-section">
                    <h3>اشترك في نشرتنا البريدية</h3>
                    <p>احصل على آخر الأخبار والمقالات مباشرة في بريدك الإلكتروني</p>
                    <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                        {% csrf_token %}
                        <div class="input-group mb-3">
                            <input type="email" class="form-control" name="email" placeholder="بريدك الإلكتروني" required>
                            <button class="btn btn-light" type="submit">اشتراك</button>
                        </div>
                    </form>
                </div>
                
                <!-- Recent Comments -->
                {% if recent_comments %}
                <div class="sidebar">
                    <h5>آخر التعليقات</h5>
                    {% for comment in recent_comments %}
                    <div class="popular-article">
                        <div>
                            <h6>
                                <a href="{{ comment.article.get_absolute_url }}" class="text-decoration-none">
                                    {{ comment.content|truncatewords:10 }}
                                </a>
                            </h6>
                            <small class="text-muted">
                                بواسطة {{ comment.author.get_full_name }} على {{ comment.article.title|truncatewords:5 }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Reading Progress Bar -->
<div class="reading-progress position-fixed top-0 start-0 bg-primary" style="height: 3px; z-index: 1001; width: 0%;"></div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter subscription
    const newsletterForms = document.querySelectorAll('.newsletter-form');
    newsletterForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'جاري الاشتراك...';
            
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    this.reset();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                showNotification('حدث خطأ أثناء الاشتراك', 'error');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.textContent = originalText;
            });
        });
    });
});
</script>
{% endblock %}