{% extends 'base/base.html' %}
{% load static %}

{% block title %}تاتا نيوز - الصفحة الرئيسية{% endblock %}

{% block meta_description %}جريدة تاتا نيوز الإلكترونية - آخر الأخبار والمقالات مع أحدث التصاميم العصرية{% endblock %}

{% block content %}
<div class="modern-homepage">
    <!-- Modern Hero Section -->
    {% if featured_articles %}
    <section class="hero-section position-relative overflow-hidden" style="background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%); padding: 3rem 0;">
        <div class="hero-background position-absolute top-0 start-0 w-100 h-100" style="background: url('{% static "images/hero-pattern.svg" %}') no-repeat center; background-size: cover; opacity: 0.1;"></div>

        <div class="container position-relative">
            <div class="row g-4">
                <!-- Main Featured Article -->
                <div class="col-lg-8">
                    {% for article in featured_articles|slice:":1" %}
                    <div class="hero-article modern-card hover-lift position-relative overflow-hidden" style="height: 500px; border-radius: var(--radius-2xl);">
                        {% if article.featured_image %}
                            <img src="{{ article.featured_image.url }}"
                                 class="w-100 h-100 object-fit-cover hero-parallax"
                                 alt="{{ article.image_alt|default:article.title }}"
                                 style="transition: transform 0.3s ease;">
                        {% else %}
                            <div class="w-100 h-100 d-flex align-items-center justify-content-center"
                                 style="background: var(--gradient-primary);">
                                <i class="fas fa-newspaper text-white" style="font-size: 4rem; opacity: 0.3;"></i>
                            </div>
                        {% endif %}

                        <!-- Gradient Overlay -->
                        <div class="position-absolute top-0 start-0 w-100 h-100"
                             style="background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.1) 100%);"></div>

                        <!-- Content -->
                        <div class="position-absolute bottom-0 start-0 p-4 text-white w-100">
                            <div class="d-flex align-items-center mb-3">
                                <span class="badge px-3 py-2 me-3"
                                      style="background: var(--accent-red); font-size: 0.875rem; border-radius: var(--radius-lg);">
                                    <i class="fas fa-star me-1"></i>{{ article.category.name }}
                                </span>
                                {% if article.is_breaking_news %}
                                    <span class="badge bg-danger px-3 py-2" style="border-radius: var(--radius-lg);">
                                        <i class="fas fa-bolt me-1"></i>عاجل
                                    </span>
                                {% endif %}
                            </div>

                            <h1 class="hero-title mb-3" style="font-size: 2.5rem; font-weight: 700; line-height: 1.2;">
                                <a href="{{ article.get_absolute_url }}" class="text-white text-decoration-none hover-lift">
                                    {{ article.title }}
                                </a>
                            </h1>

                            <p class="hero-excerpt mb-4 text-white-50" style="font-size: 1.125rem; line-height: 1.6;">
                                {{ article.excerpt|truncatewords:25 }}
                            </p>

                            <div class="d-flex align-items-center justify-content-between">
                                <div class="article-meta d-flex align-items-center gap-3 text-white-50">
                                    <div class="d-flex align-items-center">
                                        <div class="author-avatar me-2"
                                             style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.875rem; font-weight: 600;">
                                            {{ article.author.get_full_name.0|default:article.author.username.0|upper }}
                                        </div>
                                        <span>{{ article.author.get_full_name|default:article.author.username }}</span>
                                    </div>
                                    <span>•</span>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-calendar me-1"></i>
                                        <span>{{ article.published_at|date:"d M Y" }}</span>
                                    </div>
                                    <span>•</span>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-eye me-1"></i>
                                        <span>{{ article.views_count }} مشاهدة</span>
                                    </div>
                                </div>

                                <a href="{{ article.get_absolute_url }}"
                                   class="btn btn-modern-primary">
                                    اقرأ المزيد <i class="fas fa-arrow-left ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Side Featured Articles -->
                <div class="col-lg-4">
                    <div class="d-flex flex-column gap-3 h-100">
                        {% for article in featured_articles|slice:"1:3" %}
                        <div class="side-article modern-card hover-lift flex-fill" style="min-height: 240px; border-radius: var(--radius-xl);">
                            <div class="row g-0 h-100">
                                {% if article.featured_image %}
                                <div class="col-5">
                                    <img src="{{ article.featured_image.url }}"
                                         class="w-100 h-100 object-fit-cover"
                                         alt="{{ article.image_alt|default:article.title }}"
                                         style="border-radius: var(--radius-xl) 0 0 var(--radius-xl);">
                                </div>
                                <div class="col-7">
                                {% else %}
                                <div class="col-12">
                                {% endif %}
                                    <div class="card-body p-3 h-100 d-flex flex-column">
                                        <div class="mb-2">
                                            <span class="badge px-2 py-1"
                                                  style="background: var(--primary-100); color: var(--primary-700); font-size: 0.75rem; border-radius: var(--radius-md);">
                                                {{ article.category.name }}
                                            </span>
                                        </div>

                                        <h5 class="card-title mb-2 flex-grow-1" style="font-size: 1rem; line-height: 1.4;">
                                            <a href="{{ article.get_absolute_url }}"
                                               class="text-decoration-none text-dark hover-lift">
                                                {{ article.title|truncatewords:10 }}
                                            </a>
                                        </h5>

                                        <p class="card-text text-muted small mb-2" style="font-size: 0.875rem;">
                                            {{ article.excerpt|truncatewords:12 }}
                                        </p>

                                        <div class="article-meta small text-muted mt-auto">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <span>
                                                    <i class="fas fa-user me-1"></i>
                                                    {{ article.author.get_full_name|default:article.author.username|truncatechars:15 }}
                                                </span>
                                                <span>
                                                    <i class="fas fa-calendar me-1"></i>
                                                    {{ article.published_at|date:"d M" }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Main Content Section -->
    <div class="container py-5">
        <div class="row g-4">
            <!-- Articles Section -->
            <div class="col-lg-8">
                <!-- Section Header -->
                <div class="section-header d-flex justify-content-between align-items-center mb-4 pb-3"
                     style="border-bottom: 2px solid var(--primary-100);">
                    <div>
                        <h2 class="section-title mb-1" style="font-size: 2rem; font-weight: 600; color: var(--neutral-900);">
                            <i class="fas fa-newspaper me-2 text-primary"></i>آخر الأخبار
                        </h2>
                        <p class="text-muted mb-0">تابع أحدث الأخبار والمقالات</p>
                    </div>
                    <a href="{% url 'articles:latest' %}"
                       class="btn btn-modern-primary">
                        عرض المزيد <i class="fas fa-arrow-left ms-1"></i>
                    </a>
                </div>

                <!-- Articles Grid -->
                <div class="articles-grid">
                    <div class="row g-4">
                        {% for article in articles %}
                        <div class="col-md-6">
                            <article class="modern-card article-card hover-lift h-100"
                                     style="border-radius: var(--radius-xl); overflow: hidden; transition: all var(--transition-normal);">

                                <!-- Article Image -->
                                {% if article.featured_image %}
                                <div class="article-image position-relative overflow-hidden">
                                    <img src="{{ article.featured_image.url }}"
                                         class="w-100 object-fit-cover hover-scale"
                                         alt="{{ article.image_alt|default:article.title }}"
                                         style="height: 200px; transition: transform var(--transition-normal);">

                                    <!-- Badges -->
                                    <div class="position-absolute top-0 end-0 m-3">
                                        <span class="badge px-2 py-1"
                                              style="background: var(--primary-600); color: white; border-radius: var(--radius-md); font-size: 0.75rem;">
                                            {{ article.category.name }}
                                        </span>
                                    </div>

                                    {% if article.is_breaking_news %}
                                        <div class="position-absolute top-0 start-0 m-3">
                                            <span class="badge bg-danger px-2 py-1"
                                                  style="border-radius: var(--radius-md); font-size: 0.75rem;">
                                                <i class="fas fa-bolt me-1"></i>عاجل
                                            </span>
                                        </div>
                                    {% endif %}

                                    <!-- Reading Time -->
                                    <div class="position-absolute bottom-0 start-0 m-3">
                                        <span class="badge px-2 py-1"
                                              style="background: rgba(0,0,0,0.7); color: white; border-radius: var(--radius-md); font-size: 0.75rem;">
                                            <i class="fas fa-clock me-1"></i>{{ article.content|wordcount|floatformat:0|add:"50"|div:200 }} دقائق
                                        </span>
                                    </div>
                                </div>
                                {% else %}
                                <!-- Placeholder for articles without images -->
                                <div class="article-image-placeholder d-flex align-items-center justify-content-center"
                                     style="height: 200px; background: var(--gradient-primary);">
                                    <i class="fas fa-newspaper text-white" style="font-size: 3rem; opacity: 0.3;"></i>
                                </div>
                                {% endif %}

                                <!-- Article Content -->
                                <div class="card-body p-4 d-flex flex-column">
                                    <!-- Article Title -->
                                    <h3 class="article-title mb-3" style="font-size: 1.25rem; font-weight: 600; line-height: 1.4;">
                                        <a href="{{ article.get_absolute_url }}"
                                           class="text-decoration-none text-dark hover-lift"
                                           style="transition: color var(--transition-fast);">
                                            {{ article.title }}
                                        </a>
                                    </h3>

                                    <!-- Article Excerpt -->
                                    <p class="article-excerpt text-muted flex-grow-1 mb-3"
                                       style="font-size: 0.95rem; line-height: 1.6;">
                                        {{ article.excerpt|truncatewords:20 }}
                                    </p>

                                    <!-- Article Meta -->
                                    <div class="article-meta mt-auto">
                                        <div class="d-flex align-items-center justify-content-between mb-3">
                                            <!-- Author Info -->
                                            <div class="d-flex align-items-center">
                                                <div class="author-avatar me-2"
                                                     style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.875rem; font-weight: 600;">
                                                    {{ article.author.get_full_name.0|default:article.author.username.0|upper }}
                                                </div>
                                                <div>
                                                    <div class="author-name" style="font-size: 0.875rem; font-weight: 500; color: var(--neutral-700);">
                                                        {{ article.author.get_full_name|default:article.author.username }}
                                                    </div>
                                                    <div class="article-date text-muted" style="font-size: 0.75rem;">
                                                        {{ article.published_at|date:"d M Y" }}
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Stats -->
                                            <div class="article-stats d-flex align-items-center gap-3 text-muted" style="font-size: 0.875rem;">
                                                <span class="d-flex align-items-center">
                                                    <i class="fas fa-eye me-1"></i>{{ article.views_count }}
                                                </span>
                                                <span class="d-flex align-items-center">
                                                    <i class="fas fa-heart me-1"></i>{{ article.likes_count }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Tags -->
                                        {% if article.tags.exists %}
                                        <div class="article-tags d-flex flex-wrap gap-1">
                                            {% for tag in article.tags.all|slice:":3" %}
                                                <a href="{% url 'articles:tag_detail' tag.slug %}"
                                                   class="tag-link px-2 py-1 text-decoration-none"
                                                   style="background: var(--primary-50); color: var(--primary-700); border-radius: var(--radius-sm); font-size: 0.75rem; transition: all var(--transition-fast);">
                                                    #{{ tag.name }}
                                                </a>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </article>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Modern Pagination -->
                {% if is_paginated %}
                <nav aria-label="تنقل الصفحات" class="mt-5">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link modern-card border-0 me-1" href="?page=1" style="border-radius: var(--radius-md);">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link modern-card border-0 me-1" href="?page={{ page_obj.previous_page_number }}" style="border-radius: var(--radius-md);">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link border-0 me-1"
                                  style="background: var(--gradient-primary); border-radius: var(--radius-md);">
                                {{ page_obj.number }} من {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link modern-card border-0 me-1" href="?page={{ page_obj.next_page_number }}" style="border-radius: var(--radius-md);">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link modern-card border-0" href="?page={{ page_obj.paginator.num_pages }}" style="border-radius: var(--radius-md);">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>

            <!-- Modern Sidebar -->
            <div class="col-lg-4">
                <div class="sidebar-container">
                    <!-- Popular Articles Widget -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-fire me-2 text-danger"></i>الأكثر قراءة
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            {% for article in popular_articles %}
                            <div class="popular-article-item d-flex mb-3 {% if not forloop.last %}pb-3{% endif %} {% if not forloop.last %}border-bottom border-light{% endif %}">
                                {% if article.featured_image %}
                                <div class="article-thumb me-3 flex-shrink-0">
                                    <img src="{{ article.featured_image.url }}"
                                         alt="{{ article.title }}"
                                         class="rounded hover-scale"
                                         style="width: 60px; height: 60px; object-fit: cover; border-radius: var(--radius-md);">
                                </div>
                                {% endif %}
                                <div class="article-info flex-grow-1">
                                    <h6 class="article-title mb-1" style="font-size: 0.9rem; line-height: 1.4;">
                                        <a href="{{ article.get_absolute_url }}"
                                           class="text-decoration-none text-dark hover-lift"
                                           style="transition: color var(--transition-fast);">
                                            {{ article.title|truncatewords:8 }}
                                        </a>
                                    </h6>
                                    <div class="article-stats small text-muted d-flex align-items-center">
                                        <span class="me-3">
                                            <i class="fas fa-eye me-1"></i>{{ article.views_count }}
                                        </span>
                                        <span>
                                            <i class="fas fa-calendar me-1"></i>{{ article.published_at|date:"d M" }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Categories Widget -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-folder me-2 text-primary"></i>الأقسام
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            <div class="categories-grid">
                                {% for category in categories %}
                                <a href="{{ category.get_absolute_url }}"
                                   class="category-link d-flex align-items-center p-2 mb-2 text-decoration-none rounded hover-lift"
                                   style="background: var(--neutral-50); border-radius: var(--radius-md); transition: all var(--transition-fast);">
                                    {% if category.icon %}
                                        <i class="{{ category.icon }} me-2 text-primary"></i>
                                    {% else %}
                                        <i class="fas fa-folder me-2 text-primary"></i>
                                    {% endif %}
                                    <span class="text-dark" style="font-size: 0.9rem;">{{ category.name }}</span>
                                </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <!-- Newsletter Widget -->
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl); background: var(--gradient-primary); color: white;">
                        <div class="widget-content p-4">
                            <div class="text-center mb-3">
                                <i class="fas fa-envelope-open text-white mb-2" style="font-size: 2rem; opacity: 0.8;"></i>
                                <h5 class="widget-title mb-2 text-white" style="font-weight: 600;">
                                    اشترك في نشرتنا البريدية
                                </h5>
                                <p class="text-white-50 mb-3" style="font-size: 0.9rem;">
                                    احصل على آخر الأخبار والمقالات مباشرة في بريدك الإلكتروني
                                </p>
                            </div>

                            <form method="POST" action="{% url 'newsletter:subscribe' %}" class="newsletter-form">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <input type="email"
                                           class="modern-input w-100"
                                           name="email"
                                           placeholder="بريدك الإلكتروني"
                                           required
                                           style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white;">
                                </div>
                                <button class="btn btn-light w-100" type="submit" style="border-radius: var(--radius-md); font-weight: 500;">
                                    <i class="fas fa-paper-plane me-1"></i>اشتراك الآن
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Recent Comments Widget -->
                    {% if recent_comments %}
                    <div class="sidebar-widget modern-card mb-4" style="border-radius: var(--radius-xl);">
                        <div class="widget-header p-4 pb-3" style="border-bottom: 1px solid var(--neutral-200);">
                            <h5 class="widget-title mb-0 d-flex align-items-center" style="font-weight: 600; color: var(--neutral-900);">
                                <i class="fas fa-comments me-2 text-success"></i>آخر التعليقات
                            </h5>
                        </div>
                        <div class="widget-content p-4">
                            {% for comment in recent_comments %}
                            <div class="comment-item mb-3 {% if not forloop.last %}pb-3 border-bottom border-light{% endif %}">
                                <div class="d-flex align-items-start">
                                    <div class="commenter-avatar me-2 flex-shrink-0"
                                         style="width: 32px; height: 32px; background: var(--gradient-primary); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                        {{ comment.author.get_full_name.0|default:comment.author.username.0|upper }}
                                    </div>
                                    <div class="comment-content flex-grow-1">
                                        <p class="comment-text mb-1" style="font-size: 0.875rem; line-height: 1.4;">
                                            <a href="{{ comment.article.get_absolute_url }}"
                                               class="text-decoration-none text-dark hover-lift">
                                                "{{ comment.content|truncatewords:8 }}"
                                            </a>
                                        </p>
                                        <small class="text-muted">
                                            بواسطة <strong>{{ comment.author.get_full_name|default:comment.author.username }}</strong>
                                            على {{ comment.article.title|truncatewords:4 }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    /* Homepage specific styles */
    .hero-article:hover .hero-parallax {
        transform: scale(1.05);
    }

    .category-link:hover {
        background: var(--primary-50) !important;
        transform: translateX(5px);
    }

    .tag-link:hover {
        background: var(--primary-600) !important;
        color: white !important;
    }

    .article-card:hover {
        box-shadow: var(--shadow-xl);
    }

    .article-card:hover .hover-scale {
        transform: scale(1.1);
    }

    /* Newsletter form styles */
    .newsletter-form .modern-input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .newsletter-form .modern-input:focus {
        background: rgba(255, 255, 255, 0.3) !important;
        border-color: rgba(255, 255, 255, 0.5) !important;
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2) !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 1.75rem !important;
        }

        .hero-excerpt {
            font-size: 1rem !important;
        }

        .article-image {
            height: 180px !important;
        }

        .sidebar-widget {
            margin-bottom: 1.5rem !important;
        }
    }

    /* Animation delays for staggered effect */
    .article-card:nth-child(1) { animation-delay: 0.1s; }
    .article-card:nth-child(2) { animation-delay: 0.2s; }
    .article-card:nth-child(3) { animation-delay: 0.3s; }
    .article-card:nth-child(4) { animation-delay: 0.4s; }
    .article-card:nth-child(5) { animation-delay: 0.5s; }
    .article-card:nth-child(6) { animation-delay: 0.6s; }
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modern homepage features
    initHomepageFeatures();

    function initHomepageFeatures() {
        // Parallax effect for hero section
        initParallaxEffect();

        // Staggered animations for article cards
        initStaggeredAnimations();

        // Enhanced hover effects
        initHoverEffects();

        // Newsletter subscription with modern UX
        initNewsletterSubscription();

        // Lazy loading for images
        initLazyLoading();
    }

    function initParallaxEffect() {
        const heroElements = document.querySelectorAll('.hero-parallax');

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.3;

            heroElements.forEach(element => {
                element.style.transform = `translateY(${rate}px)`;
            });
        });
    }

    function initStaggeredAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.classList.add('fade-in');
                    }, index * 100);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.article-card').forEach(card => {
            observer.observe(card);
        });
    }

    function initHoverEffects() {
        // Enhanced card hover effects
        document.querySelectorAll('.article-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px)';
                this.style.boxShadow = 'var(--shadow-xl)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'var(--shadow-sm)';
            });
        });

        // Sidebar widget hover effects
        document.querySelectorAll('.sidebar-widget').forEach(widget => {
            widget.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            widget.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }

    function initNewsletterSubscription() {
        const newsletterForms = document.querySelectorAll('.newsletter-form');

        newsletterForms.forEach(form => {
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const originalContent = submitBtn.innerHTML;
                const emailInput = this.querySelector('input[type="email"]');

                // Validate email
                if (!emailInput.value || !isValidEmail(emailInput.value)) {
                    showModernNotification('يرجى إدخال بريد إلكتروني صحيح', 'error');
                    return;
                }

                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاشتراك...';

                try {
                    const formData = new FormData(this);
                    const response = await fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        },
                    });

                    const data = await response.json();

                    if (data.success) {
                        showModernNotification('تم الاشتراك بنجاح في النشرة البريدية! 🎉', 'success');
                        this.reset();

                        // Add success animation
                        submitBtn.innerHTML = '<i class="fas fa-check me-1"></i>تم الاشتراك!';
                        submitBtn.classList.add('btn-success');

                        setTimeout(() => {
                            submitBtn.innerHTML = originalContent;
                            submitBtn.classList.remove('btn-success');
                        }, 3000);
                    } else {
                        showModernNotification(data.message || 'حدث خطأ أثناء الاشتراك', 'error');
                    }
                } catch (error) {
                    showModernNotification('حدث خطأ في الاتصال', 'error');
                } finally {
                    submitBtn.disabled = false;
                    if (!submitBtn.classList.contains('btn-success')) {
                        submitBtn.innerHTML = originalContent;
                    }
                }
            });
        });
    }

    function initLazyLoading() {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('loading-skeleton');
                        img.classList.add('fade-in');
                        observer.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            img.classList.add('loading-skeleton');
            imageObserver.observe(img);
        });
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function showModernNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed modern-card border-0`;
        notification.style.cssText = `
            top: 20px;
            left: 20px;
            z-index: 9999;
            min-width: 350px;
            max-width: 500px;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-xl);
        `;

        const icon = type === 'success' ? 'check-circle' :
                    type === 'error' ? 'exclamation-circle' : 'info-circle';

        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${icon} me-2 fs-5"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                const bsAlert = new bootstrap.Alert(notification);
                bsAlert.close();
            }
        }, 5000);
    }
});
</script>
{% endblock %}