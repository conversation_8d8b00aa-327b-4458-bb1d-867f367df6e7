from django import forms
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field
from crispy_forms.bootstrap import FormActions
from .models import Subscriber, Newsletter

class SubscriberForm(forms.ModelForm):
    """نموذج الاشتراك في النشرة البريدية"""
    
    class Meta:
        model = Subscriber
        fields = ['email', 'name']
        widgets = {
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'بريدك الإلكتروني',
                'required': True
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسمك (اختياري)'
            }),
        }
        labels = {
            'email': 'البريد الإلكتروني',
            'name': 'الاسم',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'email',
            'name',
            FormActions(
                Submit('submit', 'اشتراك', css_class='btn btn-primary'),
            )
        )
    
    def clean_email(self):
        email = self.cleaned_data.get('email', '').lower().strip()
        
        # التحقق من وجود المشترك النشط
        if Subscriber.objects.filter(email=email, is_active=True).exists():
            raise forms.ValidationError('هذا البريد الإلكتروني مشترك بالفعل.')
        
        return email

class PreferencesForm(forms.ModelForm):
    """نموذج تفضيلات الاشتراك"""
    
    class Meta:
        model = Subscriber
        fields = ['name', 'daily_digest', 'weekly_digest', 'breaking_news']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسمك'
            }),
            'daily_digest': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'weekly_digest': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'breaking_news': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        labels = {
            'name': 'الاسم',
            'daily_digest': 'الملخص اليومي',
            'weekly_digest': 'الملخص الأسبوعي',
            'breaking_news': 'الأخبار العاجلة',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'name',
            Field('daily_digest', wrapper_class='form-check'),
            Field('weekly_digest', wrapper_class='form-check'),
            Field('breaking_news', wrapper_class='form-check'),
            FormActions(
                Submit('submit', 'حفظ التفضيلات', css_class='btn btn-primary'),
            )
        )

class NewsletterForm(forms.ModelForm):
    """نموذج إنشاء النشرة البريدية"""
    
    class Meta:
        model = Newsletter
        fields = ['title', 'content', 'newsletter_type', 'scheduled_at']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'عنوان النشرة'
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 10,
                'placeholder': 'محتوى النشرة'
            }),
            'newsletter_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'scheduled_at': forms.DateTimeInput(attrs={
                'class': 'form-control',
                'type': 'datetime-local'
            }),
        }
        labels = {
            'title': 'العنوان',
            'content': 'المحتوى',
            'newsletter_type': 'نوع النشرة',
            'scheduled_at': 'موعد الإرسال',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'title',
            Row(
                Column('newsletter_type', css_class='col-md-6'),
                Column('scheduled_at', css_class='col-md-6'),
            ),
            'content',
            FormActions(
                Submit('submit', 'حفظ النشرة', css_class='btn btn-primary'),
                Submit('send', 'إرسال فوري', css_class='btn btn-success ms-2'),
            )
        )