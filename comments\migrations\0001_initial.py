# Generated by Django 5.1.5 on 2025-07-21 23:57

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('articles', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض')], default='pending', max_length=20, verbose_name='الحالة')),
                ('likes_count', models.PositiveIntegerField(default=0, verbose_name='عدد الإعجابات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('article', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='articles.article', verbose_name='المقال')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='الكاتب')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='comments.comment', verbose_name='التعليق الأصلي')),
            ],
            options={
                'verbose_name': 'تعليق',
                'verbose_name_plural': 'التعليقات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommentLike',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='comments.comment')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'إعجاب تعليق',
                'verbose_name_plural': 'إعجابات التعليقات',
            },
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['article', 'status'], name='comments_co_article_7160d5_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['author', 'status'], name='comments_co_author__34963c_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='commentlike',
            unique_together={('comment', 'user')},
        ),
    ]
