from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import CustomUser

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = [
        'username', 'email', 'first_name', 'last_name',
        'role', 'is_verified', 'is_active', 'is_staff',
        'articles_count', 'created_at'
    ]
    list_filter = [
        'role', 'is_verified', 'is_active', 'is_staff',
        'is_superuser', 'created_at', 'last_login'
    ]
    search_fields = ['username', 'email', 'first_name', 'last_name']
    readonly_fields = ['created_at', 'updated_at', 'avatar_preview']
    
    fieldsets = UserAdmin.fieldsets + (
        ('معلومات إضافية', {
            'fields': (
                'role', 'bio', 'avatar', 'avatar_preview',
                'phone', 'birth_date', 'is_verified'
            )
        }),
        ('التواريخ', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('معلومات إضافية', {
            'fields': (
                'email', 'first_name', 'last_name',
                'role', 'bio', 'phone', 'birth_date'
            )
        }),
    )
    
    def avatar_preview(self, obj):
        if obj.avatar:
            return mark_safe(f'<img src="{obj.avatar.url}" style="max-height: 100px; max-width: 100px; border-radius: 50%;">')
        return "لا توجد صورة"
    avatar_preview.short_description = 'معاينة الصورة الشخصية'
    
    def articles_count(self, obj):
        count = obj.articles.filter(status='published').count()
        return f'{count} مقال'
    articles_count.short_description = 'عدد المقالات'
    
    actions = ['make_verified', 'make_unverified', 'promote_to_author', 'promote_to_editor']
    
    def make_verified(self, request, queryset):
        updated = queryset.update(is_verified=True)
        self.message_user(request, f'تم التحقق من {updated} مستخدم.')
    make_verified.short_description = 'التحقق من المستخدمين المحددين'
    
    def make_unverified(self, request, queryset):
        updated = queryset.update(is_verified=False)
        self.message_user(request, f'تم إلغاء التحقق من {updated} مستخدم.')
    make_unverified.short_description = 'إلغاء التحقق من المستخدمين'
    
    def promote_to_author(self, request, queryset):
        updated = queryset.update(role='author')
        self.message_user(request, f'تم ترقية {updated} مستخدم إلى كاتب.')
    promote_to_author.short_description = 'ترقية إلى كاتب'
    
    def promote_to_editor(self, request, queryset):
        updated = queryset.update(role='editor')
        self.message_user(request, f'تم ترقية {updated} مستخدم إلى محرر.')
    promote_to_editor.short_description = 'ترقية إلى محرر'
