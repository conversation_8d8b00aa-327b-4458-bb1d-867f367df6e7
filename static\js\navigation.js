// Enhanced Navigation JavaScript

class EnhancedNavigation {
    constructor() {
        this.init();
    }

    init() {
        this.setupStickyNavigation();
        this.setupLiveDateTime();
        this.setupSearchEnhancements();
        this.setupThemeToggle();
        this.setupMobileMenu();
        this.setupVoiceSearch();
        this.setupNotifications();
    }

    setupStickyNavigation() {
        const nav = document.getElementById('mainNavigation');
        if (!nav) return;

        let lastScrollTop = 0;
        const navHeight = nav.offsetHeight;

        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > navHeight) {
                nav.classList.add('sticky');
                
                // Hide/show on scroll direction
                if (scrollTop > lastScrollTop && scrollTop > navHeight * 2) {
                    nav.style.transform = 'translateY(-100%)';
                } else {
                    nav.style.transform = 'translateY(0)';
                }
            } else {
                nav.classList.remove('sticky');
                nav.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }

    setupLiveDateTime() {
        const dateElement = document.getElementById('live-date');
        const timeElement = document.getElementById('live-time');
        
        if (!dateElement || !timeElement) return;

        const updateDateTime = () => {
            const now = new Date();
            
            // Update date
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            dateElement.textContent = now.toLocaleDateString('ar-SA', dateOptions);
            
            // Update time
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            };
            timeElement.textContent = now.toLocaleTimeString('ar-SA', timeOptions);
        };

        updateDateTime();
        setInterval(updateDateTime, 1000);
    }

    setupSearchEnhancements() {
        const searchInput = document.querySelector('.search-input-enhanced');
        const searchSuggestions = document.querySelector('.search-suggestions');
        
        if (!searchInput || !searchSuggestions) return;

        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            
            clearTimeout(searchTimeout);
            
            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    this.fetchSearchSuggestions(query);
                }, 300);
            } else {
                this.hideSearchSuggestions();
            }
        });

        searchInput.addEventListener('focus', () => {
            if (searchInput.value.trim().length >= 2) {
                this.showSearchSuggestions();
            }
        });

        document.addEventListener('click', (e) => {
            if (!e.target.closest('.search-enhanced')) {
                this.hideSearchSuggestions();
            }
        });

        // Keyboard navigation for suggestions
        searchInput.addEventListener('keydown', (e) => {
            const suggestions = document.querySelectorAll('.suggestion-item');
            const currentActive = document.querySelector('.suggestion-item.active');
            
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateSuggestions(suggestions, currentActive, 'down');
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateSuggestions(suggestions, currentActive, 'up');
            } else if (e.key === 'Enter' && currentActive) {
                e.preventDefault();
                currentActive.click();
            } else if (e.key === 'Escape') {
                this.hideSearchSuggestions();
            }
        });
    }

    async fetchSearchSuggestions(query) {
        try {
            const response = await fetch(`/api/search/suggestions/?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.suggestions && data.suggestions.length > 0) {
                this.renderSearchSuggestions(data.suggestions);
                this.showSearchSuggestions();
            } else {
                this.hideSearchSuggestions();
            }
        } catch (error) {
            console.error('Error fetching search suggestions:', error);
            this.hideSearchSuggestions();
        }
    }

    renderSearchSuggestions(suggestions) {
        const suggestionsList = document.querySelector('.suggestions-list');
        if (!suggestionsList) return;

        suggestionsList.innerHTML = suggestions.map(suggestion => `
            <div class="suggestion-item p-2 rounded hover-lift cursor-pointer" 
                 data-query="${suggestion.query}">
                <div class="d-flex align-items-center">
                    <i class="fas fa-search me-2 text-muted"></i>
                    <span>${suggestion.query}</span>
                    ${suggestion.count ? `<small class="text-muted ms-auto">${suggestion.count} نتيجة</small>` : ''}
                </div>
            </div>
        `).join('');

        // Add click handlers
        suggestionsList.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const query = item.dataset.query;
                document.querySelector('.search-input-enhanced').value = query;
                document.querySelector('.search-form-enhanced').submit();
            });
        });
    }

    showSearchSuggestions() {
        const suggestions = document.querySelector('.search-suggestions');
        if (suggestions) {
            suggestions.classList.remove('d-none');
            suggestions.classList.add('show');
        }
    }

    hideSearchSuggestions() {
        const suggestions = document.querySelector('.search-suggestions');
        if (suggestions) {
            suggestions.classList.add('d-none');
            suggestions.classList.remove('show');
        }
    }

    navigateSuggestions(suggestions, currentActive, direction) {
        if (suggestions.length === 0) return;

        let nextIndex = 0;
        
        if (currentActive) {
            currentActive.classList.remove('active');
            const currentIndex = Array.from(suggestions).indexOf(currentActive);
            
            if (direction === 'down') {
                nextIndex = (currentIndex + 1) % suggestions.length;
            } else {
                nextIndex = currentIndex === 0 ? suggestions.length - 1 : currentIndex - 1;
            }
        }
        
        suggestions[nextIndex].classList.add('active');
        suggestions[nextIndex].scrollIntoView({ block: 'nearest' });
    }

    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle-enhanced');
        const themeIcon = document.getElementById('theme-icon-enhanced');
        
        if (!themeToggle || !themeIcon) return;

        // Set initial icon
        const currentTheme = document.documentElement.getAttribute('data-bs-theme');
        themeIcon.className = currentTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = document.documentElement.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update icon with animation
            themeIcon.style.transform = 'scale(0)';
            setTimeout(() => {
                themeIcon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                themeIcon.style.transform = 'scale(1)';
            }, 150);
            
            // Add theme transition effect
            document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
        });
    }

    setupMobileMenu() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.getElementById('mobileNavMenu');
        
        if (!mobileToggle || !mobileMenu) return;

        mobileToggle.addEventListener('click', () => {
            const isExpanded = mobileToggle.getAttribute('aria-expanded') === 'true';
            mobileToggle.setAttribute('aria-expanded', !isExpanded);
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.navigation-menu-enhanced')) {
                mobileToggle.setAttribute('aria-expanded', 'false');
                const bsCollapse = bootstrap.Collapse.getInstance(mobileMenu);
                if (bsCollapse) {
                    bsCollapse.hide();
                }
            }
        });
    }

    setupVoiceSearch() {
        const voiceBtn = document.querySelector('.voice-search-btn');
        const searchInput = document.querySelector('.search-input-enhanced');
        
        if (!voiceBtn || !searchInput || !('webkitSpeechRecognition' in window)) {
            if (voiceBtn) voiceBtn.style.display = 'none';
            return;
        }

        const recognition = new webkitSpeechRecognition();
        recognition.lang = 'ar-SA';
        recognition.continuous = false;
        recognition.interimResults = false;

        voiceBtn.addEventListener('click', () => {
            if (voiceBtn.classList.contains('recording')) {
                recognition.stop();
            } else {
                recognition.start();
            }
        });

        recognition.onstart = () => {
            voiceBtn.classList.add('recording');
            voiceBtn.innerHTML = '<i class="fas fa-stop"></i>';
        };

        recognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            searchInput.value = transcript;
            searchInput.focus();
        };

        recognition.onend = () => {
            voiceBtn.classList.remove('recording');
            voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        };

        recognition.onerror = (event) => {
            console.error('Speech recognition error:', event.error);
            voiceBtn.classList.remove('recording');
            voiceBtn.innerHTML = '<i class="fas fa-microphone"></i>';
        };
    }

    setupNotifications() {
        // Simulate real-time notifications
        this.simulateNotifications();
        
        // Mark notifications as read
        document.querySelectorAll('.notifications-dropdown .dropdown-item').forEach(item => {
            item.addEventListener('click', () => {
                item.style.opacity = '0.7';
            });
        });
    }

    simulateNotifications() {
        // This would typically connect to a WebSocket or polling mechanism
        setInterval(() => {
            const badge = document.querySelector('.notifications-dropdown .badge');
            if (badge && Math.random() > 0.95) {
                const currentCount = parseInt(badge.textContent) || 0;
                badge.textContent = currentCount + 1;
                
                // Add notification animation
                badge.style.animation = 'bounce 0.5s ease-in-out';
                setTimeout(() => {
                    badge.style.animation = '';
                }, 500);
            }
        }, 10000);
    }
}

// Initialize enhanced navigation
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedNavigation();
});

// Export for use in other scripts
window.EnhancedNavigation = EnhancedNavigation;
